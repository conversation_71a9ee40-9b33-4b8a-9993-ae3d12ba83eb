import { gql } from '@apollo/client';
import * as Apollo from '@apollo/client';
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
export type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = { [_ in K]?: never };
export type Incremental<T> = T | { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never };
const defaultOptions = {} as const;
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string; }
  String: { input: string; output: string; }
  Boolean: { input: boolean; output: boolean; }
  Int: { input: number; output: number; }
  Float: { input: number; output: number; }
  Date: { input: any; output: any; }
  DateTime: { input: any; output: any; }
  Decimal: { input: any; output: any; }
  GenericScalar: { input: any; output: any; }
  JSONString: { input: any; output: any; }
};

export type CreateEmailTemplate = {
  __typename?: 'CreateEmailTemplate';
  emailTemplate?: Maybe<EmailTemplateType>;
};

export type CreateFieldMutation = {
  __typename?: 'CreateFieldMutation';
  field?: Maybe<FieldType>;
};

export type CreateNotification = {
  __typename?: 'CreateNotification';
  notification?: Maybe<NotificationType>;
};

export type CreateProjectMutation = {
  __typename?: 'CreateProjectMutation';
  project?: Maybe<ProjectType>;
};

export type CreateRule = {
  __typename?: 'CreateRule';
  rule?: Maybe<RuleType>;
};

export type CreateSubphaseMutation = {
  __typename?: 'CreateSubphaseMutation';
  subphase?: Maybe<SubphaseType>;
};

export type DeleteEmailTemplate = {
  __typename?: 'DeleteEmailTemplate';
  ok?: Maybe<Scalars['Boolean']['output']>;
};

export type DeleteFieldMutation = {
  __typename?: 'DeleteFieldMutation';
  success?: Maybe<Scalars['Boolean']['output']>;
};

export type DeleteNotification = {
  __typename?: 'DeleteNotification';
  ok?: Maybe<Scalars['Boolean']['output']>;
};

export type DeleteProjectMutation = {
  __typename?: 'DeleteProjectMutation';
  success?: Maybe<Scalars['Boolean']['output']>;
};

export type DeleteRule = {
  __typename?: 'DeleteRule';
  ok?: Maybe<Scalars['Boolean']['output']>;
};

export type DeleteSubphaseMutation = {
  __typename?: 'DeleteSubphaseMutation';
  success?: Maybe<Scalars['Boolean']['output']>;
};

export type EmailTemplateType = {
  __typename?: 'EmailTemplateType';
  body: Scalars['String']['output'];
  createdAt: Scalars['DateTime']['output'];
  id: Scalars['ID']['output'];
  isExternal: Scalars['Boolean']['output'];
  name: Scalars['String']['output'];
  subject: Scalars['String']['output'];
  topic?: Maybe<Scalars['String']['output']>;
};

export type FieldInput = {
  description?: InputMaybe<Scalars['String']['input']>;
  hierarchy?: InputMaybe<Scalars['Int']['input']>;
  isMilestone?: InputMaybe<Scalars['Boolean']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  nameEn?: InputMaybe<Scalars['String']['input']>;
  selectionOptions?: InputMaybe<Scalars['JSONString']['input']>;
  subphaseId?: InputMaybe<Scalars['ID']['input']>;
  subtasks?: InputMaybe<Scalars['JSONString']['input']>;
  type?: InputMaybe<Scalars['String']['input']>;
  weight?: InputMaybe<Scalars['Decimal']['input']>;
};

export type FieldType = {
  __typename?: 'FieldType';
  createdAt: Scalars['DateTime']['output'];
  description: Scalars['String']['output'];
  fieldsCount?: Maybe<Scalars['Int']['output']>;
  hierarchy: Scalars['Int']['output'];
  id: Scalars['ID']['output'];
  isMilestone: Scalars['Boolean']['output'];
  name: Scalars['String']['output'];
  nameEn: Scalars['String']['output'];
  originFieldRules: Array<RuleType>;
  selectionOptions?: Maybe<Scalars['JSONString']['output']>;
  subphase: SubphaseType;
  subtasks?: Maybe<Scalars['JSONString']['output']>;
  targetFieldRules: Array<RuleType>;
  templates: Array<TemplateType>;
  triggerNotifications: Array<NotificationType>;
  type?: Maybe<Scalars['String']['output']>;
  weight: FieldsFieldWeightChoices;
};

/** An enumeration. */
export enum FieldsFieldWeightChoices {
  /** 0 */
  A_0 = 'A_0',
  /** 0.5 */
  A_0_5 = 'A_0_5',
  /** 0.25 */
  A_0_25 = 'A_0_25',
  /** 0.75 */
  A_0_75 = 'A_0_75',
  /** 1 */
  A_1 = 'A_1'
}

export type Mutation = {
  __typename?: 'Mutation';
  createEmailTemplate?: Maybe<CreateEmailTemplate>;
  createField?: Maybe<CreateFieldMutation>;
  createNotification?: Maybe<CreateNotification>;
  createProject?: Maybe<CreateProjectMutation>;
  createRule?: Maybe<CreateRule>;
  createSubphase?: Maybe<CreateSubphaseMutation>;
  deleteEmailTemplate?: Maybe<DeleteEmailTemplate>;
  deleteField?: Maybe<DeleteFieldMutation>;
  deleteNotification?: Maybe<DeleteNotification>;
  deleteProject?: Maybe<DeleteProjectMutation>;
  deleteRule?: Maybe<DeleteRule>;
  deleteSubphase?: Maybe<DeleteSubphaseMutation>;
  refreshToken?: Maybe<Refresh>;
  /** Obtain JSON Web Token mutation */
  tokenAuth?: Maybe<ObtainJsonWebToken>;
  updateEmailTemplate?: Maybe<UpdateEmailTemplate>;
  updateField?: Maybe<UpdateFieldMutation>;
  updateNotification?: Maybe<UpdateNotification>;
  updateProject?: Maybe<UpdateProjectMutation>;
  updateRule?: Maybe<UpdateRule>;
  updateSubphase?: Maybe<UpdateSubphaseMutation>;
  verifyToken?: Maybe<Verify>;
};


export type MutationCreateEmailTemplateArgs = {
  body: Scalars['String']['input'];
  isExternal?: InputMaybe<Scalars['Boolean']['input']>;
  name: Scalars['String']['input'];
  subject: Scalars['String']['input'];
  topic?: InputMaybe<Scalars['String']['input']>;
};


export type MutationCreateFieldArgs = {
  input: FieldInput;
};


export type MutationCreateNotificationArgs = {
  notificationData: NotificationInput;
};


export type MutationCreateProjectArgs = {
  input: ProjectInput;
};


export type MutationCreateRuleArgs = {
  ruleData: RuleInput;
};


export type MutationCreateSubphaseArgs = {
  input: SubphaseInput;
};


export type MutationDeleteEmailTemplateArgs = {
  id: Scalars['ID']['input'];
};


export type MutationDeleteFieldArgs = {
  id: Scalars['Int']['input'];
};


export type MutationDeleteNotificationArgs = {
  id: Scalars['ID']['input'];
};


export type MutationDeleteProjectArgs = {
  id?: InputMaybe<Scalars['Int']['input']>;
};


export type MutationDeleteRuleArgs = {
  id: Scalars['ID']['input'];
};


export type MutationDeleteSubphaseArgs = {
  id: Scalars['Int']['input'];
};


export type MutationRefreshTokenArgs = {
  token?: InputMaybe<Scalars['String']['input']>;
};


export type MutationTokenAuthArgs = {
  email: Scalars['String']['input'];
  password: Scalars['String']['input'];
};


export type MutationUpdateEmailTemplateArgs = {
  body?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['ID']['input'];
  isExternal?: InputMaybe<Scalars['Boolean']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  subject?: InputMaybe<Scalars['String']['input']>;
  topic?: InputMaybe<Scalars['String']['input']>;
};


export type MutationUpdateFieldArgs = {
  id: Scalars['Int']['input'];
  input?: InputMaybe<FieldInput>;
};


export type MutationUpdateNotificationArgs = {
  description?: InputMaybe<Scalars['String']['input']>;
  descriptionEn?: InputMaybe<Scalars['String']['input']>;
  emailTemplateId?: InputMaybe<Scalars['ID']['input']>;
  id: Scalars['ID']['input'];
  leaderRecipients?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  name?: InputMaybe<Scalars['String']['input']>;
  nameEn?: InputMaybe<Scalars['String']['input']>;
  roleRecipients?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  statusValue?: InputMaybe<Scalars['String']['input']>;
  teamRecipients?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  triggerCondition?: InputMaybe<Scalars['String']['input']>;
  triggerFieldId?: InputMaybe<Scalars['ID']['input']>;
  userRecipients?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  value?: InputMaybe<Scalars['String']['input']>;
};


export type MutationUpdateProjectArgs = {
  id: Scalars['Int']['input'];
  input: ProjectInput;
};


export type MutationUpdateRuleArgs = {
  action?: InputMaybe<Scalars['String']['input']>;
  condition?: InputMaybe<Scalars['String']['input']>;
  description?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['ID']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
  originFieldId?: InputMaybe<Scalars['ID']['input']>;
  rule?: InputMaybe<Scalars['String']['input']>;
  status?: InputMaybe<Scalars['String']['input']>;
  targetFieldId?: InputMaybe<Scalars['ID']['input']>;
  value?: InputMaybe<Scalars['String']['input']>;
};


export type MutationUpdateSubphaseArgs = {
  id: Scalars['Int']['input'];
  input?: InputMaybe<SubphaseInput>;
};


export type MutationVerifyTokenArgs = {
  token?: InputMaybe<Scalars['String']['input']>;
};

export type NotificationInput = {
  description?: InputMaybe<Scalars['String']['input']>;
  descriptionEn?: InputMaybe<Scalars['String']['input']>;
  emailTemplateId: Scalars['ID']['input'];
  leaderRecipients?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  name?: InputMaybe<Scalars['String']['input']>;
  nameEn?: InputMaybe<Scalars['String']['input']>;
  roleRecipients?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  statusValue?: InputMaybe<Scalars['String']['input']>;
  teamRecipients?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  triggerCondition: Scalars['String']['input'];
  triggerFieldId: Scalars['ID']['input'];
  userRecipients?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  value?: InputMaybe<Scalars['String']['input']>;
};

export type NotificationType = {
  __typename?: 'NotificationType';
  allRecipients?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  createdAt: Scalars['DateTime']['output'];
  description?: Maybe<Scalars['String']['output']>;
  descriptionEn?: Maybe<Scalars['String']['output']>;
  emailTemplate: EmailTemplateType;
  id: Scalars['ID']['output'];
  leaderRecipients?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  name: Scalars['String']['output'];
  nameEn?: Maybe<Scalars['String']['output']>;
  roleRecipients?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  teamRecipients?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  templates: Array<TemplateType>;
  triggerCondition: NotificationsNotificationTriggerConditionChoices;
  triggerField?: Maybe<FieldType>;
  userRecipients?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  value?: Maybe<Scalars['String']['output']>;
};

/** An enumeration. */
export enum NotificationsNotificationTriggerConditionChoices {
  /** Cambia */
  Changes = 'CHANGES',
  /** Contiene */
  Contains = 'CONTAINS',
  /** Es rellenado */
  IsFilled = 'IS_FILLED',
  /** Su valor es */
  ItsValueIs = 'ITS_VALUE_IS',
  /** Su estado es */
  StatusIs = 'STATUS_IS'
}

/** Obtain JSON Web Token mutation */
export type ObtainJsonWebToken = {
  __typename?: 'ObtainJSONWebToken';
  payload: Scalars['GenericScalar']['output'];
  refreshExpiresIn: Scalars['Int']['output'];
  token: Scalars['String']['output'];
};

export type PhaseType = {
  __typename?: 'PhaseType';
  id: Scalars['ID']['output'];
  name: Scalars['String']['output'];
  order: Scalars['Int']['output'];
  subphaseSet: Array<SubphaseType>;
};

export type ProjectInput = {
  aggregator?: InputMaybe<Scalars['String']['input']>;
  alias?: InputMaybe<Scalars['String']['input']>;
  backupId?: InputMaybe<Scalars['Int']['input']>;
  collectionFinalDate?: InputMaybe<Scalars['Date']['input']>;
  collectionInitialDate?: InputMaybe<Scalars['Date']['input']>;
  companyName?: InputMaybe<Scalars['String']['input']>;
  coordinatorId?: InputMaybe<Scalars['Int']['input']>;
  goliveFinalDate?: InputMaybe<Scalars['Date']['input']>;
  goliveInitialDate?: InputMaybe<Scalars['Date']['input']>;
  implementationType?: InputMaybe<Scalars['String']['input']>;
  implementer1Id?: InputMaybe<Scalars['Int']['input']>;
  implementer2Id?: InputMaybe<Scalars['Int']['input']>;
  incubadoraFinalDate?: InputMaybe<Scalars['Date']['input']>;
  incubadoraInitialDate?: InputMaybe<Scalars['Date']['input']>;
  incubatorId?: InputMaybe<Scalars['Int']['input']>;
  lid?: InputMaybe<Scalars['String']['input']>;
  migrationFinalDate?: InputMaybe<Scalars['Date']['input']>;
  migrationInitialDate?: InputMaybe<Scalars['Date']['input']>;
  month1Test?: InputMaybe<Scalars['Date']['input']>;
  month2Test?: InputMaybe<Scalars['Date']['input']>;
  startFinalDate?: InputMaybe<Scalars['Date']['input']>;
  startInitialDate?: InputMaybe<Scalars['Date']['input']>;
  status?: InputMaybe<Scalars['String']['input']>;
  teamId?: InputMaybe<Scalars['Int']['input']>;
  templateId?: InputMaybe<Scalars['Int']['input']>;
  testFinalDate?: InputMaybe<Scalars['Date']['input']>;
  testInitialDate?: InputMaybe<Scalars['Date']['input']>;
};

export type ProjectType = {
  __typename?: 'ProjectType';
  actualPhase?: Maybe<Scalars['String']['output']>;
  aggregator?: Maybe<Scalars['String']['output']>;
  alias: Scalars['String']['output'];
  backup?: Maybe<UserType>;
  collectionFinalDate?: Maybe<Scalars['Date']['output']>;
  collectionInitialDate?: Maybe<Scalars['Date']['output']>;
  collectionRealFinalDate?: Maybe<Scalars['Date']['output']>;
  collectionRealInitialDate?: Maybe<Scalars['Date']['output']>;
  companyName: Scalars['String']['output'];
  coordinator?: Maybe<UserType>;
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  goliveFinalDate?: Maybe<Scalars['Date']['output']>;
  goliveInitialDate?: Maybe<Scalars['Date']['output']>;
  goliveRealFinalDate?: Maybe<Scalars['Date']['output']>;
  goliveRealInitialDate?: Maybe<Scalars['Date']['output']>;
  id: Scalars['ID']['output'];
  implementationType: ProjectsProjectImplementationTypeChoices;
  implementer?: Maybe<UserType>;
  implementer1?: Maybe<UserType>;
  implementer2?: Maybe<UserType>;
  incubadoraFinalDate?: Maybe<Scalars['Date']['output']>;
  incubadoraInitialDate?: Maybe<Scalars['Date']['output']>;
  incubadoraRealFinalDate?: Maybe<Scalars['Date']['output']>;
  incubadoraRealInitialDate?: Maybe<Scalars['Date']['output']>;
  incubator?: Maybe<UserType>;
  lid: Scalars['String']['output'];
  migrationFinalDate?: Maybe<Scalars['Date']['output']>;
  migrationInitialDate?: Maybe<Scalars['Date']['output']>;
  migrationRealFinalDate?: Maybe<Scalars['Date']['output']>;
  migrationRealInitialDate?: Maybe<Scalars['Date']['output']>;
  month1Test?: Maybe<Scalars['Date']['output']>;
  month2Test?: Maybe<Scalars['Date']['output']>;
  percentages?: Maybe<Array<Maybe<Scalars['JSONString']['output']>>>;
  startFinalDate?: Maybe<Scalars['Date']['output']>;
  startInitialDate?: Maybe<Scalars['Date']['output']>;
  startRealFinalDate?: Maybe<Scalars['Date']['output']>;
  startRealInitialDate?: Maybe<Scalars['Date']['output']>;
  status: ProjectsProjectStatusChoices;
  team?: Maybe<TeamType>;
  template?: Maybe<TemplateType>;
  testFinalDate?: Maybe<Scalars['Date']['output']>;
  testInitialDate?: Maybe<Scalars['Date']['output']>;
  testRealFinalDate?: Maybe<Scalars['Date']['output']>;
  testRealInitialDate?: Maybe<Scalars['Date']['output']>;
};

/** An enumeration. */
export enum ProjectsProjectImplementationTypeChoices {
  /** Existente */
  Existente = 'EXISTENTE',
  /** Migración */
  Migracion = 'MIGRACION',
  /** Nueva */
  Nueva = 'NUEVA',
  /** Subrogación */
  Subrogacion = 'SUBROGACION'
}

/** An enumeration. */
export enum ProjectsProjectStatusChoices {
  /** Cancelled */
  Cancelled = 'CANCELLED',
  /** Completed */
  Completed = 'COMPLETED',
  /** In Progress */
  InProgress = 'IN_PROGRESS',
  /** On Hold */
  OnHold = 'ON_HOLD',
  /** Scheduled */
  Scheduled = 'SCHEDULED'
}

export type Query = {
  __typename?: 'Query';
  allEmailTemplates?: Maybe<Array<Maybe<EmailTemplateType>>>;
  allFields?: Maybe<Array<Maybe<FieldType>>>;
  allNotifications?: Maybe<Array<Maybe<NotificationType>>>;
  allPhases?: Maybe<Array<Maybe<PhaseType>>>;
  allProjects?: Maybe<Array<Maybe<ProjectType>>>;
  allRules?: Maybe<Array<Maybe<RuleType>>>;
  allSubphases?: Maybe<Array<Maybe<SubphaseType>>>;
  emailTemplate?: Maybe<EmailTemplateType>;
  field?: Maybe<FieldType>;
  notification?: Maybe<NotificationType>;
  phase?: Maybe<PhaseType>;
  project?: Maybe<ProjectType>;
  rule?: Maybe<RuleType>;
  subphase?: Maybe<SubphaseType>;
};


export type QueryEmailTemplateArgs = {
  id: Scalars['ID']['input'];
};


export type QueryFieldArgs = {
  id: Scalars['Int']['input'];
};


export type QueryNotificationArgs = {
  id: Scalars['Int']['input'];
};


export type QueryPhaseArgs = {
  id: Scalars['Int']['input'];
};


export type QueryProjectArgs = {
  id: Scalars['Int']['input'];
};


export type QueryRuleArgs = {
  id: Scalars['Int']['input'];
};


export type QuerySubphaseArgs = {
  id: Scalars['Int']['input'];
};

export type Refresh = {
  __typename?: 'Refresh';
  payload: Scalars['GenericScalar']['output'];
  refreshExpiresIn: Scalars['Int']['output'];
  token: Scalars['String']['output'];
};

export type RuleInput = {
  action?: InputMaybe<Scalars['String']['input']>;
  condition?: InputMaybe<Scalars['String']['input']>;
  description?: InputMaybe<Scalars['String']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  originFieldId: Scalars['ID']['input'];
  rule?: InputMaybe<Scalars['String']['input']>;
  status?: InputMaybe<Scalars['String']['input']>;
  targetFieldId: Scalars['ID']['input'];
  value?: InputMaybe<Scalars['String']['input']>;
};

export type RuleType = {
  __typename?: 'RuleType';
  action: RulesRuleActionChoices;
  condition: RulesRuleConditionChoices;
  createdAt: Scalars['DateTime']['output'];
  description?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  name: Scalars['String']['output'];
  originField: FieldType;
  rule?: Maybe<Scalars['String']['output']>;
  status: RulesRuleStatusChoices;
  targetField: FieldType;
  templateCount?: Maybe<Scalars['Int']['output']>;
  templates: Array<TemplateType>;
  value?: Maybe<Scalars['String']['output']>;
};

/** An enumeration. */
export enum RulesRuleActionChoices {
  /** Deshabilitar */
  Disable = 'DISABLE',
  /** Habilitar */
  Enable = 'ENABLE'
}

/** An enumeration. */
export enum RulesRuleConditionChoices {
  /** Contenido igual a */
  ContentEqualTo = 'CONTENT_EQUAL_TO',
  /** Contenido diferente de */
  ContentUnequalTo = 'CONTENT_UNEQUAL_TO',
  /** Tiene contenido */
  HasContent = 'HAS_CONTENT',
  /** Está en progreso */
  InProgress = 'IN_PROGRESS',
  /** Está completado */
  IsCompleted = 'IS_COMPLETED',
  /** No está completado */
  IsNotCompleted = 'IS_NOT_COMPLETED'
}

/** An enumeration. */
export enum RulesRuleStatusChoices {
  /** Active */
  Active = 'ACTIVE',
  /** Inactive */
  Inactive = 'INACTIVE'
}

export type SubphaseInput = {
  name?: InputMaybe<Scalars['String']['input']>;
  phaseId?: InputMaybe<Scalars['ID']['input']>;
};

export type SubphaseType = {
  __typename?: 'SubphaseType';
  fieldSet: Array<FieldType>;
  id: Scalars['ID']['output'];
  name: Scalars['String']['output'];
  order: Scalars['Int']['output'];
  phase: PhaseType;
};

export type TeamType = {
  __typename?: 'TeamType';
  code: Scalars['String']['output'];
  id: Scalars['ID']['output'];
  name: Scalars['String']['output'];
  projectSet: Array<ProjectType>;
  rmouserSet: Array<UserType>;
};

export type TemplateType = {
  __typename?: 'TemplateType';
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  description?: Maybe<Scalars['String']['output']>;
  fields: Array<FieldType>;
  fullDefinition?: Maybe<Scalars['JSONString']['output']>;
  id: Scalars['ID']['output'];
  isActive: Scalars['Boolean']['output'];
  name: Scalars['String']['output'];
  notifications: Array<NotificationType>;
  projectSet: Array<ProjectType>;
  rules: Array<RuleType>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
};

export type UpdateEmailTemplate = {
  __typename?: 'UpdateEmailTemplate';
  emailTemplate?: Maybe<EmailTemplateType>;
};

export type UpdateFieldMutation = {
  __typename?: 'UpdateFieldMutation';
  field?: Maybe<FieldType>;
};

export type UpdateNotification = {
  __typename?: 'UpdateNotification';
  notification?: Maybe<NotificationType>;
};

export type UpdateProjectMutation = {
  __typename?: 'UpdateProjectMutation';
  project?: Maybe<ProjectType>;
};

export type UpdateRule = {
  __typename?: 'UpdateRule';
  rule?: Maybe<RuleType>;
};

export type UpdateSubphaseMutation = {
  __typename?: 'UpdateSubphaseMutation';
  subphase?: Maybe<SubphaseType>;
};

export type UserType = {
  __typename?: 'UserType';
  allNotifications: Array<NotificationType>;
  backupProjects: Array<ProjectType>;
  coordinatedProjects1: Array<ProjectType>;
  email: Scalars['String']['output'];
  firstName?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  implementedProjects1: Array<ProjectType>;
  implementedProjects2: Array<ProjectType>;
  incubatedProjects: Array<ProjectType>;
  isActive: Scalars['Boolean']['output'];
  isStaff: Scalars['Boolean']['output'];
  isSuperuser: Scalars['Boolean']['output'];
  lastLogin?: Maybe<Scalars['DateTime']['output']>;
  lastName?: Maybe<Scalars['String']['output']>;
  leaderNotifications: Array<NotificationType>;
  mustChangePassword: Scalars['Boolean']['output'];
  name: Scalars['String']['output'];
  password: Scalars['String']['output'];
  position: Scalars['String']['output'];
  team?: Maybe<TeamType>;
  teamCode?: Maybe<Scalars['String']['output']>;
  teamNotifications: Array<NotificationType>;
  userNotifications: Array<NotificationType>;
};

export type Verify = {
  __typename?: 'Verify';
  payload: Scalars['GenericScalar']['output'];
};

export type AllEmailTemplatesQueryVariables = Exact<{ [key: string]: never; }>;


export type AllEmailTemplatesQuery = { __typename?: 'Query', allEmailTemplates?: Array<{ __typename?: 'EmailTemplateType', id: string, name: string, body: string, subject: string, topic?: string | null, isExternal: boolean } | null> | null };

export type AllFieldsQueryVariables = Exact<{ [key: string]: never; }>;


export type AllFieldsQuery = { __typename?: 'Query', allFields?: Array<{ __typename?: 'FieldType', id: string, name: string, nameEn: string, description: string, weight: FieldsFieldWeightChoices, isMilestone: boolean, type?: string | null, selectionOptions?: any | null, subtasks?: any | null, createdAt: any, fieldsCount?: number | null, hierarchy: number, subphase: { __typename?: 'SubphaseType', name: string, phase: { __typename?: 'PhaseType', name: string } } } | null> | null };

export type AllNotificationsQueryVariables = Exact<{ [key: string]: never; }>;


export type AllNotificationsQuery = { __typename?: 'Query', allNotifications?: Array<{ __typename?: 'NotificationType', id: string, name: string, description?: string | null, triggerCondition: NotificationsNotificationTriggerConditionChoices, value?: string | null, leaderRecipients?: Array<string | null> | null, teamRecipients?: Array<string | null> | null, userRecipients?: Array<string | null> | null, roleRecipients?: Array<string | null> | null, allRecipients?: Array<string | null> | null, createdAt: any, triggerField?: { __typename?: 'FieldType', id: string, name: string } | null, emailTemplate: { __typename?: 'EmailTemplateType', id: string, name: string } } | null> | null };

export type AllPhasesQueryVariables = Exact<{ [key: string]: never; }>;


export type AllPhasesQuery = { __typename?: 'Query', allPhases?: Array<{ __typename?: 'PhaseType', id: string, name: string, order: number } | null> | null };

export type AllRulesQueryVariables = Exact<{ [key: string]: never; }>;


export type AllRulesQuery = { __typename?: 'Query', allRules?: Array<{ __typename?: 'RuleType', action: RulesRuleActionChoices, condition: RulesRuleConditionChoices, description?: string | null, id: string, name: string, rule?: string | null, status: RulesRuleStatusChoices, value?: string | null, createdAt: any, templateCount?: number | null, originField: { __typename?: 'FieldType', id: string, name: string, selectionOptions?: any | null }, targetField: { __typename?: 'FieldType', id: string, name: string } } | null> | null };

export type AllSubphasesQueryVariables = Exact<{ [key: string]: never; }>;


export type AllSubphasesQuery = { __typename?: 'Query', allSubphases?: Array<{ __typename?: 'SubphaseType', id: string, name: string, order: number, phase: { __typename?: 'PhaseType', id: string, name: string } } | null> | null };

export type ExecuteCreateEmailTemplateMutationVariables = Exact<{
  name: Scalars['String']['input'];
  subject: Scalars['String']['input'];
  body: Scalars['String']['input'];
  topic: Scalars['String']['input'];
  isExternal?: InputMaybe<Scalars['Boolean']['input']>;
}>;


export type ExecuteCreateEmailTemplateMutation = { __typename?: 'Mutation', createEmailTemplate?: { __typename?: 'CreateEmailTemplate', emailTemplate?: { __typename?: 'EmailTemplateType', id: string, name: string, body: string, subject: string, topic?: string | null, isExternal: boolean } | null } | null };

export type ExecuteCreateFieldMutationVariables = Exact<{
  name: Scalars['String']['input'];
  nameEn: Scalars['String']['input'];
  type: Scalars['String']['input'];
  subphaseId: Scalars['ID']['input'];
  description?: InputMaybe<Scalars['String']['input']>;
  isMilestone?: InputMaybe<Scalars['Boolean']['input']>;
  selectionOptions?: InputMaybe<Scalars['JSONString']['input']>;
  subtasks?: InputMaybe<Scalars['JSONString']['input']>;
  weight?: InputMaybe<Scalars['Decimal']['input']>;
  hierarchy?: InputMaybe<Scalars['Int']['input']>;
}>;


export type ExecuteCreateFieldMutation = { __typename?: 'Mutation', createField?: { __typename?: 'CreateFieldMutation', field?: { __typename?: 'FieldType', id: string } | null } | null };

export type ExecuteCreateNotificationMutationVariables = Exact<{
  notificationData: NotificationInput;
}>;


export type ExecuteCreateNotificationMutation = { __typename?: 'Mutation', createNotification?: { __typename?: 'CreateNotification', notification?: { __typename?: 'NotificationType', id: string, name: string, description?: string | null, triggerCondition: NotificationsNotificationTriggerConditionChoices, value?: string | null, allRecipients?: Array<string | null> | null, triggerField?: { __typename?: 'FieldType', id: string, name: string } | null } | null } | null };

export type ExecuteCreateProjectMutationVariables = Exact<{
  input: ProjectInput;
}>;


export type ExecuteCreateProjectMutation = { __typename?: 'Mutation', createProject?: { __typename?: 'CreateProjectMutation', project?: { __typename?: 'ProjectType', id: string, lid: string, alias: string, companyName: string, aggregator?: string | null, implementationType: ProjectsProjectImplementationTypeChoices, startInitialDate?: any | null, startFinalDate?: any | null, collectionInitialDate?: any | null, collectionFinalDate?: any | null, migrationInitialDate?: any | null, migrationFinalDate?: any | null, testInitialDate?: any | null, testFinalDate?: any | null, month1Test?: any | null, month2Test?: any | null, goliveInitialDate?: any | null, goliveFinalDate?: any | null, implementer1?: { __typename?: 'UserType', id: string, name: string, email: string } | null, implementer2?: { __typename?: 'UserType', id: string, name: string, email: string } | null, backup?: { __typename?: 'UserType', id: string, name: string, email: string } | null, coordinator?: { __typename?: 'UserType', id: string, name: string, email: string } | null, team?: { __typename?: 'TeamType', id: string, name: string, code: string } | null, incubator?: { __typename?: 'UserType', id: string, name: string, email: string } | null, template?: { __typename?: 'TemplateType', id: string } | null } | null } | null };

export type ExecuteCreateRuleMutationVariables = Exact<{
  ruleData: RuleInput;
}>;


export type ExecuteCreateRuleMutation = { __typename?: 'Mutation', createRule?: { __typename?: 'CreateRule', rule?: { __typename?: 'RuleType', id: string, name: string, description?: string | null, action: RulesRuleActionChoices, condition: RulesRuleConditionChoices, status: RulesRuleStatusChoices, value?: string | null, originField: { __typename?: 'FieldType', id: string, name: string, selectionOptions?: any | null }, targetField: { __typename?: 'FieldType', id: string, name: string, selectionOptions?: any | null } } | null } | null };

export type ExecuteCreateSubphaseMutationVariables = Exact<{
  input: SubphaseInput;
}>;


export type ExecuteCreateSubphaseMutation = { __typename?: 'Mutation', createSubphase?: { __typename?: 'CreateSubphaseMutation', subphase?: { __typename?: 'SubphaseType', name: string, order: number } | null } | null };

export type ExecuteDeleteEmailTemplateMutationVariables = Exact<{
  id: Scalars['ID']['input'];
}>;


export type ExecuteDeleteEmailTemplateMutation = { __typename?: 'Mutation', deleteEmailTemplate?: { __typename?: 'DeleteEmailTemplate', ok?: boolean | null } | null };

export type ExecuteDeleteFieldMutationVariables = Exact<{
  id: Scalars['Int']['input'];
}>;


export type ExecuteDeleteFieldMutation = { __typename?: 'Mutation', deleteField?: { __typename?: 'DeleteFieldMutation', success?: boolean | null } | null };

export type ExecuteDeleteNotificationMutationVariables = Exact<{
  id: Scalars['ID']['input'];
}>;


export type ExecuteDeleteNotificationMutation = { __typename?: 'Mutation', deleteNotification?: { __typename?: 'DeleteNotification', ok?: boolean | null } | null };

export type ExecuteDeleteRuleMutationVariables = Exact<{
  id: Scalars['ID']['input'];
}>;


export type ExecuteDeleteRuleMutation = { __typename?: 'Mutation', deleteRule?: { __typename?: 'DeleteRule', ok?: boolean | null } | null };

export type ExecuteDeleteSubphaseMutationVariables = Exact<{
  id: Scalars['Int']['input'];
}>;


export type ExecuteDeleteSubphaseMutation = { __typename?: 'Mutation', deleteSubphase?: { __typename?: 'DeleteSubphaseMutation', success?: boolean | null } | null };

export type GetRuleQueryVariables = Exact<{
  id: Scalars['Int']['input'];
}>;


export type GetRuleQuery = { __typename?: 'Query', rule?: { __typename?: 'RuleType', action: RulesRuleActionChoices, condition: RulesRuleConditionChoices, description?: string | null, id: string, name: string, originField: { __typename?: 'FieldType', id: string, name: string, subtasks?: any | null, selectionOptions?: any | null }, targetField: { __typename?: 'FieldType', id: string, name: string, subtasks?: any | null, selectionOptions?: any | null } } | null };

export type ExecuteUpdateEmailTemplateMutationVariables = Exact<{
  id: Scalars['ID']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
  subject?: InputMaybe<Scalars['String']['input']>;
  body?: InputMaybe<Scalars['String']['input']>;
  topic?: InputMaybe<Scalars['String']['input']>;
  isExternal?: InputMaybe<Scalars['Boolean']['input']>;
}>;


export type ExecuteUpdateEmailTemplateMutation = { __typename?: 'Mutation', updateEmailTemplate?: { __typename?: 'UpdateEmailTemplate', emailTemplate?: { __typename?: 'EmailTemplateType', id: string, name: string, body: string, subject: string, topic?: string | null, isExternal: boolean } | null } | null };

export type ExecuteUpdateFieldMutationVariables = Exact<{
  id: Scalars['Int']['input'];
  input: FieldInput;
}>;


export type ExecuteUpdateFieldMutation = { __typename?: 'Mutation', updateField?: { __typename?: 'UpdateFieldMutation', field?: { __typename?: 'FieldType', id: string, name: string, nameEn: string, description: string, type?: string | null, weight: FieldsFieldWeightChoices, isMilestone: boolean, hierarchy: number, subphase: { __typename?: 'SubphaseType', id: string, name: string, phase: { __typename?: 'PhaseType', id: string, name: string } } } | null } | null };

export type ExecuteUpdateNotificationMutationVariables = Exact<{
  id: Scalars['ID']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
  description?: InputMaybe<Scalars['String']['input']>;
  descriptionEn?: InputMaybe<Scalars['String']['input']>;
  triggerFieldId?: InputMaybe<Scalars['ID']['input']>;
  triggerCondition?: InputMaybe<Scalars['String']['input']>;
  value?: InputMaybe<Scalars['String']['input']>;
  emailTemplateId?: InputMaybe<Scalars['ID']['input']>;
  userRecipients?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>> | InputMaybe<Scalars['String']['input']>>;
  teamRecipients?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>> | InputMaybe<Scalars['String']['input']>>;
  leaderRecipients?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>> | InputMaybe<Scalars['String']['input']>>;
  roleRecipients?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>> | InputMaybe<Scalars['String']['input']>>;
}>;


export type ExecuteUpdateNotificationMutation = { __typename?: 'Mutation', updateNotification?: { __typename?: 'UpdateNotification', notification?: { __typename?: 'NotificationType', id: string, name: string, description?: string | null, triggerCondition: NotificationsNotificationTriggerConditionChoices, value?: string | null, leaderRecipients?: Array<string | null> | null, teamRecipients?: Array<string | null> | null, userRecipients?: Array<string | null> | null, roleRecipients?: Array<string | null> | null, allRecipients?: Array<string | null> | null, createdAt: any, triggerField?: { __typename?: 'FieldType', id: string, name: string } | null, emailTemplate: { __typename?: 'EmailTemplateType', id: string, name: string } } | null } | null };

export type UpdateProjectDatesMutationVariables = Exact<{
  id: Scalars['Int']['input'];
  input: ProjectInput;
}>;


export type UpdateProjectDatesMutation = { __typename?: 'Mutation', updateProject?: { __typename?: 'UpdateProjectMutation', project?: { __typename?: 'ProjectType', aggregator?: string | null } | null } | null };

export type ExecuteUpdateRuleMutationVariables = Exact<{
  id: Scalars['ID']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
  description?: InputMaybe<Scalars['String']['input']>;
  status?: InputMaybe<Scalars['String']['input']>;
  value?: InputMaybe<Scalars['String']['input']>;
  action?: InputMaybe<Scalars['String']['input']>;
  condition?: InputMaybe<Scalars['String']['input']>;
  originFieldId?: InputMaybe<Scalars['ID']['input']>;
  targetFieldId?: InputMaybe<Scalars['ID']['input']>;
}>;


export type ExecuteUpdateRuleMutation = { __typename?: 'Mutation', updateRule?: { __typename?: 'UpdateRule', rule?: { __typename?: 'RuleType', id: string, name: string, description?: string | null, action: RulesRuleActionChoices, condition: RulesRuleConditionChoices, status: RulesRuleStatusChoices, value?: string | null, originField: { __typename?: 'FieldType', id: string, name: string, selectionOptions?: any | null }, targetField: { __typename?: 'FieldType', id: string, name: string, selectionOptions?: any | null } } | null } | null };

export type ExecuteUpdateSubphaseMutationVariables = Exact<{
  id: Scalars['Int']['input'];
  input: SubphaseInput;
}>;


export type ExecuteUpdateSubphaseMutation = { __typename?: 'Mutation', updateSubphase?: { __typename?: 'UpdateSubphaseMutation', subphase?: { __typename?: 'SubphaseType', id: string, name: string, order: number, phase: { __typename?: 'PhaseType', name: string } } | null } | null };


export const AllEmailTemplatesDocument = gql`
    query AllEmailTemplates {
  allEmailTemplates {
    id
    name
    body
    subject
    topic
    isExternal
  }
}
    `;

/**
 * __useAllEmailTemplatesQuery__
 *
 * To run a query within a React component, call `useAllEmailTemplatesQuery` and pass it any options that fit your needs.
 * When your component renders, `useAllEmailTemplatesQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useAllEmailTemplatesQuery({
 *   variables: {
 *   },
 * });
 */
export function useAllEmailTemplatesQuery(baseOptions?: Apollo.QueryHookOptions<AllEmailTemplatesQuery, AllEmailTemplatesQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<AllEmailTemplatesQuery, AllEmailTemplatesQueryVariables>(AllEmailTemplatesDocument, options);
      }
export function useAllEmailTemplatesLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<AllEmailTemplatesQuery, AllEmailTemplatesQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<AllEmailTemplatesQuery, AllEmailTemplatesQueryVariables>(AllEmailTemplatesDocument, options);
        }
export function useAllEmailTemplatesSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<AllEmailTemplatesQuery, AllEmailTemplatesQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<AllEmailTemplatesQuery, AllEmailTemplatesQueryVariables>(AllEmailTemplatesDocument, options);
        }
export type AllEmailTemplatesQueryHookResult = ReturnType<typeof useAllEmailTemplatesQuery>;
export type AllEmailTemplatesLazyQueryHookResult = ReturnType<typeof useAllEmailTemplatesLazyQuery>;
export type AllEmailTemplatesSuspenseQueryHookResult = ReturnType<typeof useAllEmailTemplatesSuspenseQuery>;
export type AllEmailTemplatesQueryResult = Apollo.QueryResult<AllEmailTemplatesQuery, AllEmailTemplatesQueryVariables>;
export const AllFieldsDocument = gql`
    query AllFields {
  allFields {
    id
    name
    nameEn
    description
    subphase {
      phase {
        name
      }
      name
    }
    weight
    isMilestone
    type
    selectionOptions
    subtasks
    createdAt
    fieldsCount
    hierarchy
  }
}
    `;

/**
 * __useAllFieldsQuery__
 *
 * To run a query within a React component, call `useAllFieldsQuery` and pass it any options that fit your needs.
 * When your component renders, `useAllFieldsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useAllFieldsQuery({
 *   variables: {
 *   },
 * });
 */
export function useAllFieldsQuery(baseOptions?: Apollo.QueryHookOptions<AllFieldsQuery, AllFieldsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<AllFieldsQuery, AllFieldsQueryVariables>(AllFieldsDocument, options);
      }
export function useAllFieldsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<AllFieldsQuery, AllFieldsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<AllFieldsQuery, AllFieldsQueryVariables>(AllFieldsDocument, options);
        }
export function useAllFieldsSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<AllFieldsQuery, AllFieldsQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<AllFieldsQuery, AllFieldsQueryVariables>(AllFieldsDocument, options);
        }
export type AllFieldsQueryHookResult = ReturnType<typeof useAllFieldsQuery>;
export type AllFieldsLazyQueryHookResult = ReturnType<typeof useAllFieldsLazyQuery>;
export type AllFieldsSuspenseQueryHookResult = ReturnType<typeof useAllFieldsSuspenseQuery>;
export type AllFieldsQueryResult = Apollo.QueryResult<AllFieldsQuery, AllFieldsQueryVariables>;
export const AllNotificationsDocument = gql`
    query AllNotifications {
  allNotifications {
    id
    name
    description
    triggerCondition
    value
    triggerField {
      id
      name
    }
    emailTemplate {
      id
      name
    }
    leaderRecipients
    teamRecipients
    userRecipients
    roleRecipients
    allRecipients
    createdAt
  }
}
    `;

/**
 * __useAllNotificationsQuery__
 *
 * To run a query within a React component, call `useAllNotificationsQuery` and pass it any options that fit your needs.
 * When your component renders, `useAllNotificationsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useAllNotificationsQuery({
 *   variables: {
 *   },
 * });
 */
export function useAllNotificationsQuery(baseOptions?: Apollo.QueryHookOptions<AllNotificationsQuery, AllNotificationsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<AllNotificationsQuery, AllNotificationsQueryVariables>(AllNotificationsDocument, options);
      }
export function useAllNotificationsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<AllNotificationsQuery, AllNotificationsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<AllNotificationsQuery, AllNotificationsQueryVariables>(AllNotificationsDocument, options);
        }
export function useAllNotificationsSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<AllNotificationsQuery, AllNotificationsQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<AllNotificationsQuery, AllNotificationsQueryVariables>(AllNotificationsDocument, options);
        }
export type AllNotificationsQueryHookResult = ReturnType<typeof useAllNotificationsQuery>;
export type AllNotificationsLazyQueryHookResult = ReturnType<typeof useAllNotificationsLazyQuery>;
export type AllNotificationsSuspenseQueryHookResult = ReturnType<typeof useAllNotificationsSuspenseQuery>;
export type AllNotificationsQueryResult = Apollo.QueryResult<AllNotificationsQuery, AllNotificationsQueryVariables>;
export const AllPhasesDocument = gql`
    query AllPhases {
  allPhases {
    id
    name
    order
  }
}
    `;

/**
 * __useAllPhasesQuery__
 *
 * To run a query within a React component, call `useAllPhasesQuery` and pass it any options that fit your needs.
 * When your component renders, `useAllPhasesQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useAllPhasesQuery({
 *   variables: {
 *   },
 * });
 */
export function useAllPhasesQuery(baseOptions?: Apollo.QueryHookOptions<AllPhasesQuery, AllPhasesQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<AllPhasesQuery, AllPhasesQueryVariables>(AllPhasesDocument, options);
      }
export function useAllPhasesLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<AllPhasesQuery, AllPhasesQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<AllPhasesQuery, AllPhasesQueryVariables>(AllPhasesDocument, options);
        }
export function useAllPhasesSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<AllPhasesQuery, AllPhasesQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<AllPhasesQuery, AllPhasesQueryVariables>(AllPhasesDocument, options);
        }
export type AllPhasesQueryHookResult = ReturnType<typeof useAllPhasesQuery>;
export type AllPhasesLazyQueryHookResult = ReturnType<typeof useAllPhasesLazyQuery>;
export type AllPhasesSuspenseQueryHookResult = ReturnType<typeof useAllPhasesSuspenseQuery>;
export type AllPhasesQueryResult = Apollo.QueryResult<AllPhasesQuery, AllPhasesQueryVariables>;
export const AllRulesDocument = gql`
    query AllRules {
  allRules {
    action
    condition
    description
    id
    name
    rule
    status
    value
    originField {
      id
      name
      selectionOptions
    }
    targetField {
      id
      name
    }
    createdAt
    templateCount
    value
  }
}
    `;

/**
 * __useAllRulesQuery__
 *
 * To run a query within a React component, call `useAllRulesQuery` and pass it any options that fit your needs.
 * When your component renders, `useAllRulesQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useAllRulesQuery({
 *   variables: {
 *   },
 * });
 */
export function useAllRulesQuery(baseOptions?: Apollo.QueryHookOptions<AllRulesQuery, AllRulesQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<AllRulesQuery, AllRulesQueryVariables>(AllRulesDocument, options);
      }
export function useAllRulesLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<AllRulesQuery, AllRulesQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<AllRulesQuery, AllRulesQueryVariables>(AllRulesDocument, options);
        }
export function useAllRulesSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<AllRulesQuery, AllRulesQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<AllRulesQuery, AllRulesQueryVariables>(AllRulesDocument, options);
        }
export type AllRulesQueryHookResult = ReturnType<typeof useAllRulesQuery>;
export type AllRulesLazyQueryHookResult = ReturnType<typeof useAllRulesLazyQuery>;
export type AllRulesSuspenseQueryHookResult = ReturnType<typeof useAllRulesSuspenseQuery>;
export type AllRulesQueryResult = Apollo.QueryResult<AllRulesQuery, AllRulesQueryVariables>;
export const AllSubphasesDocument = gql`
    query AllSubphases {
  allSubphases {
    id
    name
    order
    phase {
      id
      name
    }
  }
}
    `;

/**
 * __useAllSubphasesQuery__
 *
 * To run a query within a React component, call `useAllSubphasesQuery` and pass it any options that fit your needs.
 * When your component renders, `useAllSubphasesQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useAllSubphasesQuery({
 *   variables: {
 *   },
 * });
 */
export function useAllSubphasesQuery(baseOptions?: Apollo.QueryHookOptions<AllSubphasesQuery, AllSubphasesQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<AllSubphasesQuery, AllSubphasesQueryVariables>(AllSubphasesDocument, options);
      }
export function useAllSubphasesLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<AllSubphasesQuery, AllSubphasesQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<AllSubphasesQuery, AllSubphasesQueryVariables>(AllSubphasesDocument, options);
        }
export function useAllSubphasesSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<AllSubphasesQuery, AllSubphasesQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<AllSubphasesQuery, AllSubphasesQueryVariables>(AllSubphasesDocument, options);
        }
export type AllSubphasesQueryHookResult = ReturnType<typeof useAllSubphasesQuery>;
export type AllSubphasesLazyQueryHookResult = ReturnType<typeof useAllSubphasesLazyQuery>;
export type AllSubphasesSuspenseQueryHookResult = ReturnType<typeof useAllSubphasesSuspenseQuery>;
export type AllSubphasesQueryResult = Apollo.QueryResult<AllSubphasesQuery, AllSubphasesQueryVariables>;
export const ExecuteCreateEmailTemplateDocument = gql`
    mutation ExecuteCreateEmailTemplate($name: String!, $subject: String!, $body: String!, $topic: String!, $isExternal: Boolean) {
  createEmailTemplate(
    name: $name
    subject: $subject
    body: $body
    topic: $topic
    isExternal: $isExternal
  ) {
    emailTemplate {
      id
      name
      body
      subject
      topic
      isExternal
    }
  }
}
    `;
export type ExecuteCreateEmailTemplateMutationFn = Apollo.MutationFunction<ExecuteCreateEmailTemplateMutation, ExecuteCreateEmailTemplateMutationVariables>;

/**
 * __useExecuteCreateEmailTemplateMutation__
 *
 * To run a mutation, you first call `useExecuteCreateEmailTemplateMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useExecuteCreateEmailTemplateMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [executeCreateEmailTemplateMutation, { data, loading, error }] = useExecuteCreateEmailTemplateMutation({
 *   variables: {
 *      name: // value for 'name'
 *      subject: // value for 'subject'
 *      body: // value for 'body'
 *      topic: // value for 'topic'
 *      isExternal: // value for 'isExternal'
 *   },
 * });
 */
export function useExecuteCreateEmailTemplateMutation(baseOptions?: Apollo.MutationHookOptions<ExecuteCreateEmailTemplateMutation, ExecuteCreateEmailTemplateMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<ExecuteCreateEmailTemplateMutation, ExecuteCreateEmailTemplateMutationVariables>(ExecuteCreateEmailTemplateDocument, options);
      }
export type ExecuteCreateEmailTemplateMutationHookResult = ReturnType<typeof useExecuteCreateEmailTemplateMutation>;
export type ExecuteCreateEmailTemplateMutationResult = Apollo.MutationResult<ExecuteCreateEmailTemplateMutation>;
export type ExecuteCreateEmailTemplateMutationOptions = Apollo.BaseMutationOptions<ExecuteCreateEmailTemplateMutation, ExecuteCreateEmailTemplateMutationVariables>;
export const ExecuteCreateFieldDocument = gql`
    mutation ExecuteCreateField($name: String!, $nameEn: String!, $type: String!, $subphaseId: ID!, $description: String, $isMilestone: Boolean = false, $selectionOptions: JSONString, $subtasks: JSONString, $weight: Decimal, $hierarchy: Int) {
  createField(
    input: {name: $name, nameEn: $nameEn, type: $type, subphaseId: $subphaseId, description: $description, isMilestone: $isMilestone, selectionOptions: $selectionOptions, subtasks: $subtasks, weight: $weight, hierarchy: $hierarchy}
  ) {
    field {
      id
    }
  }
}
    `;
export type ExecuteCreateFieldMutationFn = Apollo.MutationFunction<ExecuteCreateFieldMutation, ExecuteCreateFieldMutationVariables>;

/**
 * __useExecuteCreateFieldMutation__
 *
 * To run a mutation, you first call `useExecuteCreateFieldMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useExecuteCreateFieldMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [executeCreateFieldMutation, { data, loading, error }] = useExecuteCreateFieldMutation({
 *   variables: {
 *      name: // value for 'name'
 *      nameEn: // value for 'nameEn'
 *      type: // value for 'type'
 *      subphaseId: // value for 'subphaseId'
 *      description: // value for 'description'
 *      isMilestone: // value for 'isMilestone'
 *      selectionOptions: // value for 'selectionOptions'
 *      subtasks: // value for 'subtasks'
 *      weight: // value for 'weight'
 *      hierarchy: // value for 'hierarchy'
 *   },
 * });
 */
export function useExecuteCreateFieldMutation(baseOptions?: Apollo.MutationHookOptions<ExecuteCreateFieldMutation, ExecuteCreateFieldMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<ExecuteCreateFieldMutation, ExecuteCreateFieldMutationVariables>(ExecuteCreateFieldDocument, options);
      }
export type ExecuteCreateFieldMutationHookResult = ReturnType<typeof useExecuteCreateFieldMutation>;
export type ExecuteCreateFieldMutationResult = Apollo.MutationResult<ExecuteCreateFieldMutation>;
export type ExecuteCreateFieldMutationOptions = Apollo.BaseMutationOptions<ExecuteCreateFieldMutation, ExecuteCreateFieldMutationVariables>;
export const ExecuteCreateNotificationDocument = gql`
    mutation ExecuteCreateNotification($notificationData: NotificationInput!) {
  createNotification(notificationData: $notificationData) {
    notification {
      id
      name
      description
      triggerCondition
      value
      triggerField {
        id
        name
      }
      allRecipients
    }
  }
}
    `;
export type ExecuteCreateNotificationMutationFn = Apollo.MutationFunction<ExecuteCreateNotificationMutation, ExecuteCreateNotificationMutationVariables>;

/**
 * __useExecuteCreateNotificationMutation__
 *
 * To run a mutation, you first call `useExecuteCreateNotificationMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useExecuteCreateNotificationMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [executeCreateNotificationMutation, { data, loading, error }] = useExecuteCreateNotificationMutation({
 *   variables: {
 *      notificationData: // value for 'notificationData'
 *   },
 * });
 */
export function useExecuteCreateNotificationMutation(baseOptions?: Apollo.MutationHookOptions<ExecuteCreateNotificationMutation, ExecuteCreateNotificationMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<ExecuteCreateNotificationMutation, ExecuteCreateNotificationMutationVariables>(ExecuteCreateNotificationDocument, options);
      }
export type ExecuteCreateNotificationMutationHookResult = ReturnType<typeof useExecuteCreateNotificationMutation>;
export type ExecuteCreateNotificationMutationResult = Apollo.MutationResult<ExecuteCreateNotificationMutation>;
export type ExecuteCreateNotificationMutationOptions = Apollo.BaseMutationOptions<ExecuteCreateNotificationMutation, ExecuteCreateNotificationMutationVariables>;
export const ExecuteCreateProjectDocument = gql`
    mutation ExecuteCreateProject($input: ProjectInput!) {
  createProject(input: $input) {
    project {
      id
      lid
      alias
      companyName
      aggregator
      implementationType
      startInitialDate
      startFinalDate
      collectionInitialDate
      collectionFinalDate
      migrationInitialDate
      migrationFinalDate
      testInitialDate
      testFinalDate
      month1Test
      month2Test
      goliveInitialDate
      goliveFinalDate
      implementer1 {
        id
        name
        email
      }
      implementer2 {
        id
        name
        email
      }
      backup {
        id
        name
        email
      }
      coordinator {
        id
        name
        email
      }
      team {
        id
        name
        code
      }
      incubator {
        id
        name
        email
      }
      template {
        id
      }
    }
  }
}
    `;
export type ExecuteCreateProjectMutationFn = Apollo.MutationFunction<ExecuteCreateProjectMutation, ExecuteCreateProjectMutationVariables>;

/**
 * __useExecuteCreateProjectMutation__
 *
 * To run a mutation, you first call `useExecuteCreateProjectMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useExecuteCreateProjectMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [executeCreateProjectMutation, { data, loading, error }] = useExecuteCreateProjectMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useExecuteCreateProjectMutation(baseOptions?: Apollo.MutationHookOptions<ExecuteCreateProjectMutation, ExecuteCreateProjectMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<ExecuteCreateProjectMutation, ExecuteCreateProjectMutationVariables>(ExecuteCreateProjectDocument, options);
      }
export type ExecuteCreateProjectMutationHookResult = ReturnType<typeof useExecuteCreateProjectMutation>;
export type ExecuteCreateProjectMutationResult = Apollo.MutationResult<ExecuteCreateProjectMutation>;
export type ExecuteCreateProjectMutationOptions = Apollo.BaseMutationOptions<ExecuteCreateProjectMutation, ExecuteCreateProjectMutationVariables>;
export const ExecuteCreateRuleDocument = gql`
    mutation ExecuteCreateRule($ruleData: RuleInput!) {
  createRule(ruleData: $ruleData) {
    rule {
      id
      name
      description
      action
      condition
      status
      value
      originField {
        id
        name
        selectionOptions
      }
      targetField {
        id
        name
        selectionOptions
      }
    }
  }
}
    `;
export type ExecuteCreateRuleMutationFn = Apollo.MutationFunction<ExecuteCreateRuleMutation, ExecuteCreateRuleMutationVariables>;

/**
 * __useExecuteCreateRuleMutation__
 *
 * To run a mutation, you first call `useExecuteCreateRuleMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useExecuteCreateRuleMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [executeCreateRuleMutation, { data, loading, error }] = useExecuteCreateRuleMutation({
 *   variables: {
 *      ruleData: // value for 'ruleData'
 *   },
 * });
 */
export function useExecuteCreateRuleMutation(baseOptions?: Apollo.MutationHookOptions<ExecuteCreateRuleMutation, ExecuteCreateRuleMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<ExecuteCreateRuleMutation, ExecuteCreateRuleMutationVariables>(ExecuteCreateRuleDocument, options);
      }
export type ExecuteCreateRuleMutationHookResult = ReturnType<typeof useExecuteCreateRuleMutation>;
export type ExecuteCreateRuleMutationResult = Apollo.MutationResult<ExecuteCreateRuleMutation>;
export type ExecuteCreateRuleMutationOptions = Apollo.BaseMutationOptions<ExecuteCreateRuleMutation, ExecuteCreateRuleMutationVariables>;
export const ExecuteCreateSubphaseDocument = gql`
    mutation ExecuteCreateSubphase($input: SubphaseInput!) {
  createSubphase(input: $input) {
    subphase {
      name
      order
    }
  }
}
    `;
export type ExecuteCreateSubphaseMutationFn = Apollo.MutationFunction<ExecuteCreateSubphaseMutation, ExecuteCreateSubphaseMutationVariables>;

/**
 * __useExecuteCreateSubphaseMutation__
 *
 * To run a mutation, you first call `useExecuteCreateSubphaseMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useExecuteCreateSubphaseMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [executeCreateSubphaseMutation, { data, loading, error }] = useExecuteCreateSubphaseMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useExecuteCreateSubphaseMutation(baseOptions?: Apollo.MutationHookOptions<ExecuteCreateSubphaseMutation, ExecuteCreateSubphaseMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<ExecuteCreateSubphaseMutation, ExecuteCreateSubphaseMutationVariables>(ExecuteCreateSubphaseDocument, options);
      }
export type ExecuteCreateSubphaseMutationHookResult = ReturnType<typeof useExecuteCreateSubphaseMutation>;
export type ExecuteCreateSubphaseMutationResult = Apollo.MutationResult<ExecuteCreateSubphaseMutation>;
export type ExecuteCreateSubphaseMutationOptions = Apollo.BaseMutationOptions<ExecuteCreateSubphaseMutation, ExecuteCreateSubphaseMutationVariables>;
export const ExecuteDeleteEmailTemplateDocument = gql`
    mutation ExecuteDeleteEmailTemplate($id: ID!) {
  deleteEmailTemplate(id: $id) {
    ok
  }
}
    `;
export type ExecuteDeleteEmailTemplateMutationFn = Apollo.MutationFunction<ExecuteDeleteEmailTemplateMutation, ExecuteDeleteEmailTemplateMutationVariables>;

/**
 * __useExecuteDeleteEmailTemplateMutation__
 *
 * To run a mutation, you first call `useExecuteDeleteEmailTemplateMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useExecuteDeleteEmailTemplateMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [executeDeleteEmailTemplateMutation, { data, loading, error }] = useExecuteDeleteEmailTemplateMutation({
 *   variables: {
 *      id: // value for 'id'
 *   },
 * });
 */
export function useExecuteDeleteEmailTemplateMutation(baseOptions?: Apollo.MutationHookOptions<ExecuteDeleteEmailTemplateMutation, ExecuteDeleteEmailTemplateMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<ExecuteDeleteEmailTemplateMutation, ExecuteDeleteEmailTemplateMutationVariables>(ExecuteDeleteEmailTemplateDocument, options);
      }
export type ExecuteDeleteEmailTemplateMutationHookResult = ReturnType<typeof useExecuteDeleteEmailTemplateMutation>;
export type ExecuteDeleteEmailTemplateMutationResult = Apollo.MutationResult<ExecuteDeleteEmailTemplateMutation>;
export type ExecuteDeleteEmailTemplateMutationOptions = Apollo.BaseMutationOptions<ExecuteDeleteEmailTemplateMutation, ExecuteDeleteEmailTemplateMutationVariables>;
export const ExecuteDeleteFieldDocument = gql`
    mutation ExecuteDeleteField($id: Int!) {
  deleteField(id: $id) {
    success
  }
}
    `;
export type ExecuteDeleteFieldMutationFn = Apollo.MutationFunction<ExecuteDeleteFieldMutation, ExecuteDeleteFieldMutationVariables>;

/**
 * __useExecuteDeleteFieldMutation__
 *
 * To run a mutation, you first call `useExecuteDeleteFieldMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useExecuteDeleteFieldMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [executeDeleteFieldMutation, { data, loading, error }] = useExecuteDeleteFieldMutation({
 *   variables: {
 *      id: // value for 'id'
 *   },
 * });
 */
export function useExecuteDeleteFieldMutation(baseOptions?: Apollo.MutationHookOptions<ExecuteDeleteFieldMutation, ExecuteDeleteFieldMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<ExecuteDeleteFieldMutation, ExecuteDeleteFieldMutationVariables>(ExecuteDeleteFieldDocument, options);
      }
export type ExecuteDeleteFieldMutationHookResult = ReturnType<typeof useExecuteDeleteFieldMutation>;
export type ExecuteDeleteFieldMutationResult = Apollo.MutationResult<ExecuteDeleteFieldMutation>;
export type ExecuteDeleteFieldMutationOptions = Apollo.BaseMutationOptions<ExecuteDeleteFieldMutation, ExecuteDeleteFieldMutationVariables>;
export const ExecuteDeleteNotificationDocument = gql`
    mutation ExecuteDeleteNotification($id: ID!) {
  deleteNotification(id: $id) {
    ok
  }
}
    `;
export type ExecuteDeleteNotificationMutationFn = Apollo.MutationFunction<ExecuteDeleteNotificationMutation, ExecuteDeleteNotificationMutationVariables>;

/**
 * __useExecuteDeleteNotificationMutation__
 *
 * To run a mutation, you first call `useExecuteDeleteNotificationMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useExecuteDeleteNotificationMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [executeDeleteNotificationMutation, { data, loading, error }] = useExecuteDeleteNotificationMutation({
 *   variables: {
 *      id: // value for 'id'
 *   },
 * });
 */
export function useExecuteDeleteNotificationMutation(baseOptions?: Apollo.MutationHookOptions<ExecuteDeleteNotificationMutation, ExecuteDeleteNotificationMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<ExecuteDeleteNotificationMutation, ExecuteDeleteNotificationMutationVariables>(ExecuteDeleteNotificationDocument, options);
      }
export type ExecuteDeleteNotificationMutationHookResult = ReturnType<typeof useExecuteDeleteNotificationMutation>;
export type ExecuteDeleteNotificationMutationResult = Apollo.MutationResult<ExecuteDeleteNotificationMutation>;
export type ExecuteDeleteNotificationMutationOptions = Apollo.BaseMutationOptions<ExecuteDeleteNotificationMutation, ExecuteDeleteNotificationMutationVariables>;
export const ExecuteDeleteRuleDocument = gql`
    mutation ExecuteDeleteRule($id: ID!) {
  deleteRule(id: $id) {
    ok
  }
}
    `;
export type ExecuteDeleteRuleMutationFn = Apollo.MutationFunction<ExecuteDeleteRuleMutation, ExecuteDeleteRuleMutationVariables>;

/**
 * __useExecuteDeleteRuleMutation__
 *
 * To run a mutation, you first call `useExecuteDeleteRuleMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useExecuteDeleteRuleMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [executeDeleteRuleMutation, { data, loading, error }] = useExecuteDeleteRuleMutation({
 *   variables: {
 *      id: // value for 'id'
 *   },
 * });
 */
export function useExecuteDeleteRuleMutation(baseOptions?: Apollo.MutationHookOptions<ExecuteDeleteRuleMutation, ExecuteDeleteRuleMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<ExecuteDeleteRuleMutation, ExecuteDeleteRuleMutationVariables>(ExecuteDeleteRuleDocument, options);
      }
export type ExecuteDeleteRuleMutationHookResult = ReturnType<typeof useExecuteDeleteRuleMutation>;
export type ExecuteDeleteRuleMutationResult = Apollo.MutationResult<ExecuteDeleteRuleMutation>;
export type ExecuteDeleteRuleMutationOptions = Apollo.BaseMutationOptions<ExecuteDeleteRuleMutation, ExecuteDeleteRuleMutationVariables>;
export const ExecuteDeleteSubphaseDocument = gql`
    mutation ExecuteDeleteSubphase($id: Int!) {
  deleteSubphase(id: $id) {
    success
  }
}
    `;
export type ExecuteDeleteSubphaseMutationFn = Apollo.MutationFunction<ExecuteDeleteSubphaseMutation, ExecuteDeleteSubphaseMutationVariables>;

/**
 * __useExecuteDeleteSubphaseMutation__
 *
 * To run a mutation, you first call `useExecuteDeleteSubphaseMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useExecuteDeleteSubphaseMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [executeDeleteSubphaseMutation, { data, loading, error }] = useExecuteDeleteSubphaseMutation({
 *   variables: {
 *      id: // value for 'id'
 *   },
 * });
 */
export function useExecuteDeleteSubphaseMutation(baseOptions?: Apollo.MutationHookOptions<ExecuteDeleteSubphaseMutation, ExecuteDeleteSubphaseMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<ExecuteDeleteSubphaseMutation, ExecuteDeleteSubphaseMutationVariables>(ExecuteDeleteSubphaseDocument, options);
      }
export type ExecuteDeleteSubphaseMutationHookResult = ReturnType<typeof useExecuteDeleteSubphaseMutation>;
export type ExecuteDeleteSubphaseMutationResult = Apollo.MutationResult<ExecuteDeleteSubphaseMutation>;
export type ExecuteDeleteSubphaseMutationOptions = Apollo.BaseMutationOptions<ExecuteDeleteSubphaseMutation, ExecuteDeleteSubphaseMutationVariables>;
export const GetRuleDocument = gql`
    query GetRule($id: Int!) {
  rule(id: $id) {
    action
    condition
    description
    id
    name
    originField {
      id
      name
      subtasks
      selectionOptions
    }
    targetField {
      id
      name
      subtasks
      selectionOptions
    }
  }
}
    `;

/**
 * __useGetRuleQuery__
 *
 * To run a query within a React component, call `useGetRuleQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetRuleQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetRuleQuery({
 *   variables: {
 *      id: // value for 'id'
 *   },
 * });
 */
export function useGetRuleQuery(baseOptions: Apollo.QueryHookOptions<GetRuleQuery, GetRuleQueryVariables> & ({ variables: GetRuleQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetRuleQuery, GetRuleQueryVariables>(GetRuleDocument, options);
      }
export function useGetRuleLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetRuleQuery, GetRuleQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetRuleQuery, GetRuleQueryVariables>(GetRuleDocument, options);
        }
export function useGetRuleSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetRuleQuery, GetRuleQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetRuleQuery, GetRuleQueryVariables>(GetRuleDocument, options);
        }
export type GetRuleQueryHookResult = ReturnType<typeof useGetRuleQuery>;
export type GetRuleLazyQueryHookResult = ReturnType<typeof useGetRuleLazyQuery>;
export type GetRuleSuspenseQueryHookResult = ReturnType<typeof useGetRuleSuspenseQuery>;
export type GetRuleQueryResult = Apollo.QueryResult<GetRuleQuery, GetRuleQueryVariables>;
export const ExecuteUpdateEmailTemplateDocument = gql`
    mutation ExecuteUpdateEmailTemplate($id: ID!, $name: String, $subject: String, $body: String, $topic: String, $isExternal: Boolean) {
  updateEmailTemplate(
    id: $id
    name: $name
    subject: $subject
    body: $body
    topic: $topic
    isExternal: $isExternal
  ) {
    emailTemplate {
      id
      name
      body
      subject
      topic
      isExternal
    }
  }
}
    `;
export type ExecuteUpdateEmailTemplateMutationFn = Apollo.MutationFunction<ExecuteUpdateEmailTemplateMutation, ExecuteUpdateEmailTemplateMutationVariables>;

/**
 * __useExecuteUpdateEmailTemplateMutation__
 *
 * To run a mutation, you first call `useExecuteUpdateEmailTemplateMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useExecuteUpdateEmailTemplateMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [executeUpdateEmailTemplateMutation, { data, loading, error }] = useExecuteUpdateEmailTemplateMutation({
 *   variables: {
 *      id: // value for 'id'
 *      name: // value for 'name'
 *      subject: // value for 'subject'
 *      body: // value for 'body'
 *      topic: // value for 'topic'
 *      isExternal: // value for 'isExternal'
 *   },
 * });
 */
export function useExecuteUpdateEmailTemplateMutation(baseOptions?: Apollo.MutationHookOptions<ExecuteUpdateEmailTemplateMutation, ExecuteUpdateEmailTemplateMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<ExecuteUpdateEmailTemplateMutation, ExecuteUpdateEmailTemplateMutationVariables>(ExecuteUpdateEmailTemplateDocument, options);
      }
export type ExecuteUpdateEmailTemplateMutationHookResult = ReturnType<typeof useExecuteUpdateEmailTemplateMutation>;
export type ExecuteUpdateEmailTemplateMutationResult = Apollo.MutationResult<ExecuteUpdateEmailTemplateMutation>;
export type ExecuteUpdateEmailTemplateMutationOptions = Apollo.BaseMutationOptions<ExecuteUpdateEmailTemplateMutation, ExecuteUpdateEmailTemplateMutationVariables>;
export const ExecuteUpdateFieldDocument = gql`
    mutation ExecuteUpdateField($id: Int!, $input: FieldInput!) {
  updateField(id: $id, input: $input) {
    field {
      id
      name
      nameEn
      description
      type
      weight
      isMilestone
      subphase {
        id
        name
        phase {
          id
          name
        }
      }
      hierarchy
    }
  }
}
    `;
export type ExecuteUpdateFieldMutationFn = Apollo.MutationFunction<ExecuteUpdateFieldMutation, ExecuteUpdateFieldMutationVariables>;

/**
 * __useExecuteUpdateFieldMutation__
 *
 * To run a mutation, you first call `useExecuteUpdateFieldMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useExecuteUpdateFieldMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [executeUpdateFieldMutation, { data, loading, error }] = useExecuteUpdateFieldMutation({
 *   variables: {
 *      id: // value for 'id'
 *      input: // value for 'input'
 *   },
 * });
 */
export function useExecuteUpdateFieldMutation(baseOptions?: Apollo.MutationHookOptions<ExecuteUpdateFieldMutation, ExecuteUpdateFieldMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<ExecuteUpdateFieldMutation, ExecuteUpdateFieldMutationVariables>(ExecuteUpdateFieldDocument, options);
      }
export type ExecuteUpdateFieldMutationHookResult = ReturnType<typeof useExecuteUpdateFieldMutation>;
export type ExecuteUpdateFieldMutationResult = Apollo.MutationResult<ExecuteUpdateFieldMutation>;
export type ExecuteUpdateFieldMutationOptions = Apollo.BaseMutationOptions<ExecuteUpdateFieldMutation, ExecuteUpdateFieldMutationVariables>;
export const ExecuteUpdateNotificationDocument = gql`
    mutation ExecuteUpdateNotification($id: ID!, $name: String, $description: String, $descriptionEn: String, $triggerFieldId: ID, $triggerCondition: String, $value: String, $emailTemplateId: ID, $userRecipients: [String], $teamRecipients: [String], $leaderRecipients: [String], $roleRecipients: [String]) {
  updateNotification(
    id: $id
    name: $name
    description: $description
    descriptionEn: $descriptionEn
    triggerFieldId: $triggerFieldId
    triggerCondition: $triggerCondition
    value: $value
    emailTemplateId: $emailTemplateId
    userRecipients: $userRecipients
    teamRecipients: $teamRecipients
    leaderRecipients: $leaderRecipients
    roleRecipients: $roleRecipients
  ) {
    notification {
      id
      name
      description
      triggerCondition
      value
      triggerField {
        id
        name
      }
      emailTemplate {
        id
        name
      }
      leaderRecipients
      teamRecipients
      userRecipients
      roleRecipients
      allRecipients
      createdAt
    }
  }
}
    `;
export type ExecuteUpdateNotificationMutationFn = Apollo.MutationFunction<ExecuteUpdateNotificationMutation, ExecuteUpdateNotificationMutationVariables>;

/**
 * __useExecuteUpdateNotificationMutation__
 *
 * To run a mutation, you first call `useExecuteUpdateNotificationMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useExecuteUpdateNotificationMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [executeUpdateNotificationMutation, { data, loading, error }] = useExecuteUpdateNotificationMutation({
 *   variables: {
 *      id: // value for 'id'
 *      name: // value for 'name'
 *      description: // value for 'description'
 *      descriptionEn: // value for 'descriptionEn'
 *      triggerFieldId: // value for 'triggerFieldId'
 *      triggerCondition: // value for 'triggerCondition'
 *      value: // value for 'value'
 *      emailTemplateId: // value for 'emailTemplateId'
 *      userRecipients: // value for 'userRecipients'
 *      teamRecipients: // value for 'teamRecipients'
 *      leaderRecipients: // value for 'leaderRecipients'
 *      roleRecipients: // value for 'roleRecipients'
 *   },
 * });
 */
export function useExecuteUpdateNotificationMutation(baseOptions?: Apollo.MutationHookOptions<ExecuteUpdateNotificationMutation, ExecuteUpdateNotificationMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<ExecuteUpdateNotificationMutation, ExecuteUpdateNotificationMutationVariables>(ExecuteUpdateNotificationDocument, options);
      }
export type ExecuteUpdateNotificationMutationHookResult = ReturnType<typeof useExecuteUpdateNotificationMutation>;
export type ExecuteUpdateNotificationMutationResult = Apollo.MutationResult<ExecuteUpdateNotificationMutation>;
export type ExecuteUpdateNotificationMutationOptions = Apollo.BaseMutationOptions<ExecuteUpdateNotificationMutation, ExecuteUpdateNotificationMutationVariables>;
export const UpdateProjectDatesDocument = gql`
    mutation UpdateProjectDates($id: Int!, $input: ProjectInput!) {
  updateProject(id: $id, input: $input) {
    project {
      aggregator
    }
  }
}
    `;
export type UpdateProjectDatesMutationFn = Apollo.MutationFunction<UpdateProjectDatesMutation, UpdateProjectDatesMutationVariables>;

/**
 * __useUpdateProjectDatesMutation__
 *
 * To run a mutation, you first call `useUpdateProjectDatesMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUpdateProjectDatesMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [updateProjectDatesMutation, { data, loading, error }] = useUpdateProjectDatesMutation({
 *   variables: {
 *      id: // value for 'id'
 *      input: // value for 'input'
 *   },
 * });
 */
export function useUpdateProjectDatesMutation(baseOptions?: Apollo.MutationHookOptions<UpdateProjectDatesMutation, UpdateProjectDatesMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<UpdateProjectDatesMutation, UpdateProjectDatesMutationVariables>(UpdateProjectDatesDocument, options);
      }
export type UpdateProjectDatesMutationHookResult = ReturnType<typeof useUpdateProjectDatesMutation>;
export type UpdateProjectDatesMutationResult = Apollo.MutationResult<UpdateProjectDatesMutation>;
export type UpdateProjectDatesMutationOptions = Apollo.BaseMutationOptions<UpdateProjectDatesMutation, UpdateProjectDatesMutationVariables>;
export const ExecuteUpdateRuleDocument = gql`
    mutation ExecuteUpdateRule($id: ID!, $name: String, $description: String, $status: String, $value: String, $action: String, $condition: String, $originFieldId: ID, $targetFieldId: ID) {
  updateRule(
    id: $id
    name: $name
    description: $description
    status: $status
    value: $value
    action: $action
    condition: $condition
    originFieldId: $originFieldId
    targetFieldId: $targetFieldId
  ) {
    rule {
      id
      name
      description
      action
      condition
      status
      value
      originField {
        id
        name
        selectionOptions
      }
      targetField {
        id
        name
        selectionOptions
      }
    }
  }
}
    `;
export type ExecuteUpdateRuleMutationFn = Apollo.MutationFunction<ExecuteUpdateRuleMutation, ExecuteUpdateRuleMutationVariables>;

/**
 * __useExecuteUpdateRuleMutation__
 *
 * To run a mutation, you first call `useExecuteUpdateRuleMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useExecuteUpdateRuleMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [executeUpdateRuleMutation, { data, loading, error }] = useExecuteUpdateRuleMutation({
 *   variables: {
 *      id: // value for 'id'
 *      name: // value for 'name'
 *      description: // value for 'description'
 *      status: // value for 'status'
 *      value: // value for 'value'
 *      action: // value for 'action'
 *      condition: // value for 'condition'
 *      originFieldId: // value for 'originFieldId'
 *      targetFieldId: // value for 'targetFieldId'
 *   },
 * });
 */
export function useExecuteUpdateRuleMutation(baseOptions?: Apollo.MutationHookOptions<ExecuteUpdateRuleMutation, ExecuteUpdateRuleMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<ExecuteUpdateRuleMutation, ExecuteUpdateRuleMutationVariables>(ExecuteUpdateRuleDocument, options);
      }
export type ExecuteUpdateRuleMutationHookResult = ReturnType<typeof useExecuteUpdateRuleMutation>;
export type ExecuteUpdateRuleMutationResult = Apollo.MutationResult<ExecuteUpdateRuleMutation>;
export type ExecuteUpdateRuleMutationOptions = Apollo.BaseMutationOptions<ExecuteUpdateRuleMutation, ExecuteUpdateRuleMutationVariables>;
export const ExecuteUpdateSubphaseDocument = gql`
    mutation ExecuteUpdateSubphase($id: Int!, $input: SubphaseInput!) {
  updateSubphase(id: $id, input: $input) {
    subphase {
      id
      name
      order
      phase {
        name
      }
    }
  }
}
    `;
export type ExecuteUpdateSubphaseMutationFn = Apollo.MutationFunction<ExecuteUpdateSubphaseMutation, ExecuteUpdateSubphaseMutationVariables>;

/**
 * __useExecuteUpdateSubphaseMutation__
 *
 * To run a mutation, you first call `useExecuteUpdateSubphaseMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useExecuteUpdateSubphaseMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [executeUpdateSubphaseMutation, { data, loading, error }] = useExecuteUpdateSubphaseMutation({
 *   variables: {
 *      id: // value for 'id'
 *      input: // value for 'input'
 *   },
 * });
 */
export function useExecuteUpdateSubphaseMutation(baseOptions?: Apollo.MutationHookOptions<ExecuteUpdateSubphaseMutation, ExecuteUpdateSubphaseMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<ExecuteUpdateSubphaseMutation, ExecuteUpdateSubphaseMutationVariables>(ExecuteUpdateSubphaseDocument, options);
      }
export type ExecuteUpdateSubphaseMutationHookResult = ReturnType<typeof useExecuteUpdateSubphaseMutation>;
export type ExecuteUpdateSubphaseMutationResult = Apollo.MutationResult<ExecuteUpdateSubphaseMutation>;
export type ExecuteUpdateSubphaseMutationOptions = Apollo.BaseMutationOptions<ExecuteUpdateSubphaseMutation, ExecuteUpdateSubphaseMutationVariables>;