export const API_URL =
  process.env.NEXT_PUBLIC_API_URL || "http://127.0.0.1:8000";

export const API_ROUTES = {
  LOGIN: `${API_URL}/api/token/`,
  RESET_PASSWORD: `${API_URL}/api/auth/change-password/`,
  REFRESH: `${API_URL}/api/token/refresh/`,
  USER_PROFILE: `${API_URL}/api/users/profile/`,

  ALL_USERS: `${API_URL}/api/users/crud/`,
  UPDATE_USER_ROLE: `${API_URL}/api/users/assign-role/`,
  SYNC_USERS_CORE: `${API_URL}/api/users/sync_with_core/`,
  PROJECT_USERS: `${API_URL}/api/projects/all-users-incubadora-implementacion`,

  PRODUCTION_TEAMS: `${API_URL}/api/teams/produccion-teams/`,
  PRODUCTION_TEAMS_ROBUST: `${API_URL}/api/teams/produccion-teams-robust/`,
  ALL_TEAMS: `${API_URL}/api/teams/list-teams/`,

  IMPLEMENTATION_TYPES: `${API_URL}/api/projects/implementation-types/`,
  INFO_CLIENTES: `${API_URL}/api/projects/all-clients-info/`,

  ALL_PERMISSIONS: `${API_URL}/api/users/roles/list_permissions/`,
  ALL_ROLES: `${API_URL}/api/users/roles/list_roles/`,
  CREATE_ROLE: `${API_URL}/api/users/roles/`,
  UPDATE_ROLE: `${API_URL}/api/users/roles/{id}/update_permissions/`,
  DELETE_ROLE: `${API_URL}/api/users/roles/{id}/delete/`,

  SUBPHASES_FIELDS: `${API_URL}/api/fields/list-subphases-fields/`,

  SUBPHASE_DECREASE: `${API_URL}/api/fields/decrease-order/{subphase_id}/`,
  SUBPHASE_INCREASE: `${API_URL}/api/fields/increase-order/{subphase_id}/`,

  ALL_TEMPLATES: `${API_URL}/api/templates/list/`,
  ALL_ACTIVE_TEMPLATES: `${API_URL}/api/templates/list/active/`,
  TEMPLATE_DETAIL: `${API_URL}/api/templates/detail/`,
  CREATE_TEMPLATE: `${API_URL}/api/templates/create/`,
  DISABLE_TEMPLATE: `${API_URL}/api/templates/toggle-active/{id}/`,
  UPDATE_TEMPLATE: `${API_URL}/api/templates/update-template/{id}/`,

  ACTIVE_REPORTS: `${API_URL}/api/reporting/active/`,
  ALL_REPORTS: `${API_URL}/api/reporting/list/`,
  CREATE_REPORT: `${API_URL}/api/reporting/create/`,
  DELETE_REPORT: `${API_URL}/api/reporting/delete/`,
  TOGGLE_REPORT: `${API_URL}/api/reporting/toggle/`,
  UPDATE_REPORT: `${API_URL}/api/reporting/update/`,
  GET_REPORT: `${API_URL}/api/reporting/`,
  GENERATE_PROJECT_PLAN: `${API_URL}/api/reporting/project-plan/{project_id}/`,
  GENERATE_REPORT: `${API_URL}/api/reporting/generate/{project_id}/`,

  DATE_CHANGE_PROJECT: `${API_URL}/api/projects/datechange/`,
  DATE_FIELDS: `${API_URL}/api/projects/date-fields/`,
  DATE_CHANGE_HISTORY: `${API_URL}/api/projects/datechange/logs/`,

  ALL_CONTACTS: `${API_URL}/api/contacts/crud/`,
  CREATE_CONTACT: `${API_URL}/api/contacts/crud/`,
  GET_CONTACT: `${API_URL}/api/contacts/crud/{id}/`,
  UPDATE_CONTACT: `${API_URL}/api/contacts/crud/{id}/`,
  DELETE_CONTACT: `${API_URL}/api/contacts/crud/{id}/`,
  GET_CONTACTS_BY_PROJECT: `${API_URL}/api/contacts/by-project/`,

  PROJECT_DETAIL: `${API_URL}/api/project/`,
  PROJECT_FIELDS: `${API_URL}/api/project/`,
  UPDATE_PROJECT_FIELDS: `${API_URL}/api/project/{project_id}/phases/{phase_id}/fields/update/`,
  UPDATE_SUBTASK_OBSERVATIONS: `${API_URL}/api/project/{project_id}/phases/{phase_id}/subtask-observations/update/`,
  GET_SUBTASK_OBSERVATIONS: `${API_URL}/api/project/{project_id}/phases/{phase_id}/subtask-observations/`,

  PROJECT_COMMENTS: `${API_URL}/api/project/{project_id}/phases/{phase_id}/comments/`,
  UPLOAD_PROJECT_COMMENT: `${API_URL}/api/project/{project_id}/phases/{phase_id}/comments/add/`,
  PENDING_FIELDS: `${API_URL}/api/project/{project_id}/pending-fields/`,
  VERIFY_PROJET_PHASE: `${API_URL}/api/project/{project_id}/phases/{phase_id}/verify/`,

  NOTIFICATIONS_LIST: `${API_URL}/api/notifications/{project_id}/emails/`,

  EMAIL_TOPICS: `${API_URL}/api/emails/topics/`,
};
