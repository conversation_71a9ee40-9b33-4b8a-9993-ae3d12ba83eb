import { Avatar } from "@nextui-org/react";

import { getGradientFromName } from "./utils";

export const UserAvatar = (name: string, theme: string | undefined) => {
  theme = theme || "light";
  const gradient = getGradientFromName(name, theme === "dark");

  // Get initials from name
  const getInitials = (name: string) => {
    if (!name || name.length === 0) return "NNN.";

    return (
      name
        .split(" ")
        .map((part) => part.charAt(0))
        .join(".")
        .toUpperCase()
        .substring(0, 3) + "."
    );
  };

  // Create a simple hash from name for consistent random values
  const getNameHash = (name: string) => {
    let hash = 0;

    for (let i = 0; i < name.length; i++) {
      hash = name.charCodeAt(i) + ((hash << 5) - hash);
    }

    return Math.abs(hash);
  };

  const nameHash = getNameHash(name);
  const scale = 50 + (nameHash % 151); // 50-200 range for better visibility

  return (
    <Avatar
      isBordered
      showFallback
      classNames={{
        icon: "text-black/80",
      }}
      color="primary"
      fallback={getInitials(name)}
      name={name}
      size="md"
      src={`https://api.dicebear.com/9.x/adventurer-neutral/svg?seed=${name}&scale=${scale}`}
      style={{
        background: `${gradient.from}`,
      }}
    />
  );
};
