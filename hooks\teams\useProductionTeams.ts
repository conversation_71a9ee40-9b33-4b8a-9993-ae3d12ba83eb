"use client";

import { useState } from "react";

interface ProductionTeam {
  id: number;
  name: string;
  code: string;
}

const HARDCODED_TEAMS: ProductionTeam[] = [
  { id: 2, name: "Producción 4", code: "Producción 4_PAY" },
  { id: 3, name: "Producción 2", code: "Producción 2_PAY" },
  { id: 5, name: "Producción 3", code: "Producción 3_PAY" },
  { id: 8, name: "Producción 1", code: "Producción 1_PAY" },
];

export function useProductionTeams() {
  const [productionTeams, setProductionTeams] =
    useState<ProductionTeam[]>(HARDCODED_TEAMS);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchProductionTeams = async () => {
    setLoading(true);
    try {
      setProductionTeams(HARDCODED_TEAMS);
    } catch (err: any) {
      setError(err.message || "Failed to fetch production teams");
    } finally {
      setLoading(false);
    }
  };

  return {
    productionTeams,
    loading,
    error,
    fetchProductionTeams,
  };
}
