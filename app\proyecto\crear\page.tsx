"use client";

import {
  Autocomplete,
  AutocompleteItem,
  AutocompleteSection,
} from "@heroui/autocomplete";
import { Form } from "@heroui/form";
import React, { useState, useRef, useEffect } from "react";
import { Input } from "@heroui/input";
import { DatePicker } from "@heroui/date-picker";
import { Accordion, AccordionItem } from "@heroui/accordion";
import { Button, Select, SelectItem } from "@heroui/react";
import { CalendarDate } from "@internationalized/date";
import { useRouter } from "next/navigation";
import { useMutation } from "@apollo/client";
import { I18nProvider } from "@react-aria/i18n";

import { CREATE_PROJECT } from "@/graphql/operations/projects";
import {
  ExecuteCreateProjectMutation,
  ProjectInput,
} from "@/graphql/schemas/generated";
import { title } from "@/components/primitives";
import { ConfirmModal } from "@/components/template/create/confirm-modal";
import { useProjects } from "@/hooks/projects/useProjects";
import { useProjectUsers } from "@/hooks/users/useProjectUsers";
import { useProductionTeams } from "@/hooks/teams/useProductionTeams";
import { useTemplates } from "@/hooks/templates/useTemplates";
import { mapImplementationTypeToEnum } from "@/utils/implementation-type-mapper";

export default function CrearProyectoPage() {
  const [isOpen, setIsOpen] = useState(false);
  const formRef = useRef<HTMLFormElement>(null);
  const router = useRouter();

  const [createProject, { loading }] = useMutation<
    ExecuteCreateProjectMutation,
    { input: ProjectInput }
  >(CREATE_PROJECT);

  const {
    clientInfo,
    fetchInfoClientes,
    fetchImplementationTypes,
    implementations,
  } = useProjects();

  const { projectUsers } = useProjectUsers();

  const { productionTeams, fetchProductionTeams } = useProductionTeams();

  const { templates, fetchActiveTemplates } = useTemplates();

  useEffect(() => {
    fetchInfoClientes();
    fetchImplementationTypes();
    fetchProductionTeams();
    fetchActiveTemplates();
  }, []);

  // State for Go Live dates
  const [goLiveInicioPrevisto, setGoLiveInicioPrevisto] =
    useState<CalendarDate | null>(null);
  const [goLiveFinPrevisto, setGoLiveFinPrevisto] =
    useState<CalendarDate | null>(null);

  const [selectedMonth1, setSelectedMonth1] = useState<string>("");
  const [selectedMonth2, setSelectedMonth2] = useState<string>("");

  const [selectedLID, setSelectedLID] = useState<string | null>(null);
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);

  // Track selected implementador and backup users to prevent duplicates
  const [selectedImplementador1, setSelectedImplementador1] = useState<
    string | null
  >(null);
  const [selectedImplementador2, setSelectedImplementador2] = useState<
    string | null
  >(null);
  const [selectedBackup, setSelectedBackup] = useState<string | null>(null);

  // Function to get the last day of a month
  const getLastDayOfMonth = (date: CalendarDate): CalendarDate => {
    // Create a date for the first day of the next month, then subtract one day
    const nextMonth = new CalendarDate(
      date.month === 12 ? date.year + 1 : date.year,
      date.month === 12 ? 1 : date.month + 1,
      1,
    );

    return new CalendarDate(
      date.month === 12 ? date.year + 1 : date.year,
      date.month === 12 ? 1 : date.month + 1,
      1,
    ).subtract({ days: 1 });
  };

  const monthOptions = React.useMemo(() => {
    return [...Array(24)].map((_, index) => {
      const date = new Date();

      date.setMonth(date.getMonth() + index);

      // Format as YYYY-MM-DD, setting day to 1st of month
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, "0");
      const monthYear = `${year}-${month}-01`;

      const displayText = `${date.toLocaleString("default", { month: "long" })} ${date.getFullYear()}`;

      return { key: monthYear, label: displayText };
    });
  }, []);

  const handleSelectionChange = (value: string, num: number) => {
    if (num === 1) setSelectedMonth1(value);
    if (num === 2) setSelectedMonth2(value);
  };

  // Handle Go Live Inicio Previsto change
  const handleGoLiveInicioChange = (date: CalendarDate | null) => {
    if (!date) return;
    setGoLiveInicioPrevisto(date);
    // Set Fin Previsto to the last day of the month
    const lastDay = getLastDayOfMonth(date);

    setGoLiveFinPrevisto(lastDay);
  };

  const onSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    // Collect all form data
    const formData = new FormData(e.currentTarget);
    const formDataObject: Record<string, any> = {};

    // Convert FormData to a regular object
    formData.forEach((value, key) => {
      formDataObject[key] = value;
    });

    // Add the date values (they might not be in the FormData)
    if (goLiveInicioPrevisto) {
      formDataObject["goLiveInicioPrevisto"] = goLiveInicioPrevisto.toString();
    }
    if (goLiveFinPrevisto) {
      formDataObject["goLiveFinPrevisto"] = goLiveFinPrevisto.toString();
    }

    // Open confirmation modal
    setIsOpen(true);
  };

  const handleConfirm = async () => {
    try {
      // Helper function to safely get form element value
      const getFormValue = (name: string): string => {
        const element = formRef.current?.elements.namedItem(name);

        if (
          element instanceof HTMLInputElement ||
          element instanceof HTMLSelectElement ||
          element instanceof HTMLTextAreaElement
        ) {
          return element.value;
        }

        return "";
      };

      // Map form data to GraphQL mutation variables
      const projectInput: ProjectInput = {
        lid: selectedLID || undefined,
        companyName: selectedLID
          ? clientInfo.find((client) => client.LID === parseInt(selectedLID))
            ?.client || undefined
          : undefined,
        alias: getFormValue("alias"),
        aggregator: getFormValue("agregador"),
        implementationType: mapImplementationTypeToEnum(
          getFormValue("tipologia"),
        ),

        // Team members (IDs)
        coordinatorId:
          projectUsers.find((user) => user.name === getFormValue("coordinador"))
            ?.user_id || undefined,
        backupId:
          projectUsers.find((user) => user.name === getFormValue("backup"))
            ?.user_id || undefined,
        implementer1Id:
          projectUsers.find(
            (user) => user.name === getFormValue("implementador"),
          )?.user_id || undefined,
        implementer2Id:
          projectUsers.find(
            (user) => user.name === getFormValue("implementador 2"),
          )?.user_id || undefined,
        teamId: parseInt(getFormValue("equipoProduccion")) || undefined,
        incubatorId:
          projectUsers.find((user) => user.name === getFormValue("incubadora"))
            ?.user_id || undefined,

        // Dates
        startInitialDate: getFormValue("startInicioPrevisto") || undefined,
        startFinalDate: getFormValue("startFinPrevisto") || undefined,

        collectionInitialDate:
          getFormValue("collectionInicioPrevisto") || undefined,
        collectionFinalDate: getFormValue("collectionFinPrevisto") || undefined,

        migrationInitialDate:
          getFormValue("migrationInicioPrevisto") || undefined,
        migrationFinalDate: getFormValue("migrationFinPrevisto") || undefined,

        testInitialDate: getFormValue("testInicioPrevisto") || undefined,
        testFinalDate: getFormValue("testFinPrevisto") || undefined,

        goliveInitialDate: getFormValue("goLiveInicioPrevisto") || undefined,
        goliveFinalDate: getFormValue("goLiveFinPrevisto") || undefined,

        incubadoraInitialDate:
          getFormValue("incubadoraInicioPrevisto") || undefined,
        incubadoraFinalDate: getFormValue("incubadoraFinPrevisto") || undefined,

        // Test months
        month1Test: selectedMonth1
          ? selectedMonth1.replace(/-\d+$/, "")
          : undefined,
        month2Test: selectedMonth2
          ? selectedMonth2.replace(/-\d+$/, "")
          : undefined,

        // Template
        templateId: selectedTemplate ? parseInt(selectedTemplate) : undefined,
      };

      // Execute the GraphQL mutation
      const result = await createProject({
        variables: {
          input: projectInput,
        },
      });

      // Check for successful response
      if (result.data?.createProject?.project) {
        // Navigate to the project detail page or projects list
        router.push(`/proyecto/${result.data.createProject.project.id}`);
      }
    } catch {
      // Handle error (could show an error message)
    } finally {
      setIsOpen(false);
    }
  };

  // Helper function to filter out already selected users
  const getFilteredUsers = (excludeRoles: string[]) => {
    const selectedUsers = new Set();

    if (excludeRoles.includes("implementador1") && selectedImplementador1) {
      selectedUsers.add(selectedImplementador1);
    }
    if (excludeRoles.includes("implementador2") && selectedImplementador2) {
      selectedUsers.add(selectedImplementador2);
    }
    if (excludeRoles.includes("backup") && selectedBackup) {
      selectedUsers.add(selectedBackup);
    }

    return projectUsers.filter((user) => !selectedUsers.has(user.name));
  };

  return (
    <div className="text-center w-10/12">
      <div className="pl-1 text-left">
        <h2 className={title({ size: "sm" })}>Crear proyecto</h2>
      </div>
      <Form ref={formRef} className="w-full" onSubmit={onSubmit}>
        <Accordion
          aria-label="Accordion with multiple items"
          defaultExpandedKeys={["1", "2", "3", "4"]}
          itemClasses={{
            title: "text-2xl font-bold", // Increase font size and make it bold
          }}
          keepContentMounted={true}
          selectionMode="multiple"
        >
          <AccordionItem
            key="1"
            aria-label="Datos empresa"
            className="w-full mb-4"
            title="Datos empresa"
          >
            <div className="grid grid-cols-2 gap-4 text-left">
              <Autocomplete
                isRequired
                label="LID"
                name="lid"
                placeholder="&nbsp;"
                variant="bordered"
                onSelectionChange={(selectedLID) => {
                  setSelectedLID(selectedLID ? String(selectedLID) : null);
                  if (!selectedLID || typeof selectedLID !== "string") return;

                  const selectedClient = clientInfo.find(
                    (client) => client.LID === parseInt(selectedLID),
                  );

                  if (selectedClient) {
                    const agregadorField = formRef.current?.elements.namedItem(
                      "agregador",
                    ) as HTMLInputElement;

                    if (agregadorField) {
                      agregadorField.value = selectedClient.aggregator;
                    }
                  }
                }}
              >
                {clientInfo.map((client) => (
                  <AutocompleteItem key={client.LID}>
                    {`${client.LID} - ${client.client}`}
                  </AutocompleteItem>
                ))}
              </Autocomplete>
              <div className="flex gap-4">
                <Input
                  isRequired
                  label="ALIAS"
                  name="alias"
                  placeholder="&nbsp;"
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4 mt-4 text-left">
              <Input
                isDisabled
                isRequired
                label="Agregador"
                name="agregador"
                placeholder={
                  selectedLID
                    ? clientInfo.find(
                      (client) => client.LID === parseInt(selectedLID),
                    )?.aggregator || ""
                    : ""
                }
                value={
                  selectedLID
                    ? clientInfo.find(
                      (client) => client.LID === parseInt(selectedLID),
                    )?.aggregator || ""
                    : ""
                }
              />
              <Autocomplete
                isRequired
                label="Tipología"
                name="tipologia"
                placeholder="&nbsp;"
                variant="bordered"
              >
                {implementations.map((implementation) => (
                  <AutocompleteItem key={implementation.name.toUpperCase()}>
                    {implementation.name}
                  </AutocompleteItem>
                ))}
              </Autocomplete>
            </div>
          </AccordionItem>

          <AccordionItem
            key="2"
            aria-label="Datos de implementación"
            className="w-full mb-4"
            title="Datos de implementación"
          >
            <>
              <div className="grid grid-cols-2 gap-4 text-left">
                <div>
                  <h3 className={`text-xl font-bold text-left`}>1. Start</h3>
                  <div className="flex gap-4">
                    <I18nProvider locale="es">
                      <DatePicker
                        isRequired
                        showMonthAndYearPickers
                        className="w-1/2"
                        label="Inicio previsto"
                        name="startInicioPrevisto"
                        minValue={new CalendarDate(2000, 1, 1)}
                      />
                    </I18nProvider>
                    <I18nProvider locale="es">
                      <DatePicker
                        isRequired
                        showMonthAndYearPickers
                        className="w-1/2"
                        label="Fin previsto"
                        name="startFinPrevisto"
                        minValue={new CalendarDate(2000, 1, 1)}
                      />
                    </I18nProvider>
                  </div>
                </div>
                <div>
                  <h3 className={`text-xl font-bold text-left`}>5. Go Live</h3>
                  <div className="flex gap-4">
                    <I18nProvider locale="es">
                      <DatePicker
                        isRequired
                        showMonthAndYearPickers
                        className="w-1/2"
                        label="Inicio previsto"
                        name="goLiveInicioPrevisto"
                        value={goLiveInicioPrevisto}
                        onChange={handleGoLiveInicioChange}
                        minValue={new CalendarDate(2000, 1, 1)}
                      />
                    </I18nProvider>
                    <I18nProvider locale="es">
                      <DatePicker
                        isRequired
                        showMonthAndYearPickers
                        className="w-1/2"
                        label="Fin previsto"
                        name="goLiveFinPrevisto"
                        value={goLiveFinPrevisto}
                        onChange={setGoLiveFinPrevisto}
                        minValue={new CalendarDate(2000, 1, 1)}
                      />
                    </I18nProvider>
                  </div>
                </div>
                <div>
                  <h3 className={`text-xl font-bold text-left`}>
                    2. Collection
                  </h3>
                  <div className="flex gap-4">
                    <I18nProvider locale="es">
                      <DatePicker
                        isRequired
                        showMonthAndYearPickers
                        className="w-1/2"
                        label="Inicio previsto"
                        name="collectionInicioPrevisto"
                        minValue={new CalendarDate(2000, 1, 1)}
                      />
                    </I18nProvider>
                    <I18nProvider locale="es">
                      <DatePicker
                        isRequired
                        showMonthAndYearPickers
                        className="w-1/2"
                        label="Fin previsto"
                        name="collectionFinPrevisto"
                        minValue={new CalendarDate(2000, 1, 1)}

                      />
                    </I18nProvider>
                  </div>
                </div>
                <div>
                  <h3 className="text-xl font-bold text-left">6. Take off</h3>
                  <div className="flex gap-4">
                    <I18nProvider locale="es">
                      <DatePicker
                        isRequired
                        showMonthAndYearPickers
                        className="w-1/2"
                        label="Inicio previsto"
                        name="incubadoraInicioPrevisto"
                        minValue={new CalendarDate(2000, 1, 1)}
                      />
                    </I18nProvider>
                    <I18nProvider locale="es">
                      <DatePicker
                        isRequired
                        showMonthAndYearPickers
                        className="w-1/2"
                        label="Fin previsto"
                        name="incubadoraFinPrevisto"
                        minValue={new CalendarDate(2000, 1, 1)}
                      />
                    </I18nProvider>
                  </div>
                </div>
                <div>
                  <h3 className={`text-xl font-bold text-left`}>
                    3. Migration
                  </h3>
                  <div className="flex gap-4 pb-4">
                    <I18nProvider locale="es">
                      <DatePicker
                        isRequired
                        showMonthAndYearPickers
                        className="w-1/2"
                        label="Inicio previsto"
                        name="migrationInicioPrevisto"
                        minValue={new CalendarDate(2000, 1, 1)}
                      />
                    </I18nProvider>
                    <I18nProvider locale="es">
                      <DatePicker
                        isRequired
                        showMonthAndYearPickers
                        className="w-1/2"
                        label="Fin previsto"
                        name="migrationFinPrevisto"
                        minValue={new CalendarDate(2000, 1, 1)}
                      />
                    </I18nProvider>
                  </div>
                </div>
              </div>
              <div className="text-left w-1/2 pr-2">
                <h3 className={`text-xl font-bold text-left`}>4. Test</h3>
                <div className="flex gap-4 mb-4">
                  <I18nProvider locale="es">
                    <DatePicker
                      isRequired
                      showMonthAndYearPickers
                      className="w-1/2"
                      label="Inicio previsto"
                      name="testInicioPrevisto"
                      minValue={new CalendarDate(2000, 1, 1)}
                    />
                  </I18nProvider>
                  <I18nProvider locale="es">
                    <DatePicker
                      isRequired
                      showMonthAndYearPickers
                      className="w-1/2"
                      label="Fin previsto"
                      name="testFinPrevisto"
                      minValue={new CalendarDate(2000, 1, 1)}
                    />
                  </I18nProvider>
                </div>
                <div className="flex gap-4">
                  <Select
                    isRequired
                    className="w-full"
                    label="Mes test 1"
                    placeholder="Choose a month"
                    selectedKeys={selectedMonth1 ? [selectedMonth1] : []}
                    onSelectionChange={(keys) => {
                      const selected = Array.from(keys)[0] as string;

                      handleSelectionChange(selected, 1);
                    }}
                  >
                    {monthOptions.map((month, index) => (
                      <SelectItem key={`${month.key}-${index}`}>
                        {month.label}
                      </SelectItem>
                    ))}
                  </Select>
                  <Select
                    className="w-full"
                    label="Mes test 2"
                    placeholder="Choose a month"
                    selectedKeys={selectedMonth2 ? [selectedMonth2] : []}
                    onSelectionChange={(keys) => {
                      const selected = Array.from(keys)[0] as string;

                      handleSelectionChange(selected, 2);
                    }}
                  >
                    {monthOptions.map((month, index) => (
                      <SelectItem key={`${month.key}-${index}`}>
                        {month.label}
                      </SelectItem>
                    ))}
                  </Select>
                </div>
              </div>
            </>
          </AccordionItem>

          <AccordionItem
            key="3"
            aria-label="Datos equipo"
            className="w-full mb-4"
            title="Datos equipo"
          >
            <div className="grid grid-cols-2 gap-4 text-left">
              <Autocomplete
                isRequired
                label="Coordinador"
                name="coordinador"
                placeholder="&nbsp;"
                variant="bordered"
              >
                {Object.entries(
                  projectUsers.reduce(
                    (groups, user) => {
                      const groupName = user.group_name || "Sin grupo";

                      if (!groups[groupName]) {
                        groups[groupName] = [];
                      }
                      groups[groupName].push(user);

                      return groups;
                    },
                    {} as Record<string, typeof projectUsers>,
                  ),
                ).map(([groupName, users]) => (
                  <AutocompleteSection
                    key={groupName}
                    showDivider
                    title={groupName}
                  >
                    {users
                      .sort((a, b) => a.name.localeCompare(b.name))
                      .map((user) => (
                        <AutocompleteItem key={user.name}>
                          {user.name}
                        </AutocompleteItem>
                      ))}
                  </AutocompleteSection>
                ))}
              </Autocomplete>

              <Autocomplete
                isRequired
                label="Backup implementador"
                name="backup"
                placeholder="&nbsp;"
                variant="bordered"
                onSelectionChange={(selectedUser) => {
                  setSelectedBackup(selectedUser ? String(selectedUser) : null);
                }}
              >
                {Object.entries(
                  getFilteredUsers(["implementador1", "implementador2"]).reduce(
                    (groups, user) => {
                      const groupName = user.group_name || "Sin grupo";

                      if (!groups[groupName]) {
                        groups[groupName] = [];
                      }
                      groups[groupName].push(user);

                      return groups;
                    },
                    {} as Record<string, typeof projectUsers>,
                  ),
                ).map(([groupName, users]) => (
                  <AutocompleteSection
                    key={groupName}
                    showDivider
                    title={groupName}
                  >
                    {users
                      .sort((a, b) => a.name.localeCompare(b.name))
                      .map((user) => (
                        <AutocompleteItem key={user.name}>
                          {user.name}
                        </AutocompleteItem>
                      ))}
                  </AutocompleteSection>
                ))}
              </Autocomplete>

              <Autocomplete
                isRequired
                label="Implementador"
                name="implementador"
                placeholder="&nbsp;"
                variant="bordered"
                onSelectionChange={(selectedUser) => {
                  setSelectedImplementador1(
                    selectedUser ? String(selectedUser) : null,
                  );
                }}
              >
                {Object.entries(
                  getFilteredUsers(["implementador2", "backup"]).reduce(
                    (groups, user) => {
                      const groupName = user.group_name || "Sin grupo";

                      if (!groups[groupName]) {
                        groups[groupName] = [];
                      }
                      groups[groupName].push(user);

                      return groups;
                    },
                    {} as Record<string, typeof projectUsers>,
                  ),
                ).map(([groupName, users]) => (
                  <AutocompleteSection
                    key={groupName}
                    showDivider
                    title={groupName}
                  >
                    {users
                      .sort((a, b) => a.name.localeCompare(b.name))
                      .map((user) => (
                        <AutocompleteItem key={user.name}>
                          {user.name}
                        </AutocompleteItem>
                      ))}
                  </AutocompleteSection>
                ))}
              </Autocomplete>

              <Autocomplete
                label="Incubadora"
                name="incubadora"
                placeholder="&nbsp;"
                variant="bordered"
              >
                {Object.entries(
                  projectUsers.reduce(
                    (groups, user) => {
                      const groupName = user.group_name || "Sin grupo";

                      if (!groups[groupName]) {
                        groups[groupName] = [];
                      }
                      groups[groupName].push(user);

                      return groups;
                    },
                    {} as Record<string, typeof projectUsers>,
                  ),
                ).map(([groupName, users]) => (
                  <AutocompleteSection
                    key={groupName}
                    showDivider
                    title={groupName}
                  >
                    {users
                      .sort((a, b) => a.name.localeCompare(b.name))
                      .map((user) => (
                        <AutocompleteItem key={user.name}>
                          {user.name}
                        </AutocompleteItem>
                      ))}
                  </AutocompleteSection>
                ))}
              </Autocomplete>

              <Autocomplete
                label="Implementador 2"
                name="implementador 2"
                placeholder="&nbsp;"
                variant="bordered"
                onSelectionChange={(selectedUser) => {
                  setSelectedImplementador2(
                    selectedUser ? String(selectedUser) : null,
                  );
                }}
              >
                {Object.entries(
                  getFilteredUsers(["implementador1", "backup"]).reduce(
                    (groups, user) => {
                      const groupName = user.group_name || "Sin grupo";

                      if (!groups[groupName]) {
                        groups[groupName] = [];
                      }
                      groups[groupName].push(user);

                      return groups;
                    },
                    {} as Record<string, typeof projectUsers>,
                  ),
                ).map(([groupName, users]) => (
                  <AutocompleteSection
                    key={groupName}
                    showDivider
                    title={groupName}
                  >
                    {users
                      .sort((a, b) => a.name.localeCompare(b.name))
                      .map((user) => (
                        <AutocompleteItem key={user.name}>
                          {user.name}
                        </AutocompleteItem>
                      ))}
                  </AutocompleteSection>
                ))}
              </Autocomplete>

              <Autocomplete
                label="Equipo producción"
                name="equipoProduccion"
                placeholder="&nbsp;"
                variant="bordered"
              >
                {productionTeams
                  .sort((a, b) => a.name.localeCompare(b.name))
                  .map((team) => (
                    <AutocompleteItem key={team.id}>
                      {team.name}
                    </AutocompleteItem>
                  ))}
              </Autocomplete>
            </div>
          </AccordionItem>

          <AccordionItem
            key="4"
            aria-label="Seleccionar plantilla"
            className="w-full mb-4 text-left"
            title="Seleccionar plantilla"
          >
            <Autocomplete
              isRequired
              label="Plantilla"
              name="plantilla"
              placeholder="&nbsp;"
              variant="bordered"
              onSelectionChange={(selectedTemplateId) => {
                setSelectedTemplate(
                  selectedTemplateId ? String(selectedTemplateId) : null,
                );
              }}
            >
              {templates.map((template) => (
                <AutocompleteItem key={template.id}>
                  {template.name}
                </AutocompleteItem>
              ))}
            </Autocomplete>
          </AccordionItem>
        </Accordion>

        <div className="flex gap-2 mt-4 w-full justify-end text-left">
          <Button
            color="primary"
            isDisabled={loading}
            // onPress={() => setIsOpen(true)}
            type="submit"
          >
            {loading ? "Creando..." : "Crear"}
          </Button>
          <ConfirmModal
            isOpen={isOpen}
            onClose={() => setIsOpen(false)}
            onConfirm={handleConfirm}
          />
        </div>
      </Form>
    </div>
  );
}
